import { Component, inject, TemplateRef, ViewChild } from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
} from "@angular/forms";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { SharedModule } from "src/app/shared";
import { TemplateVarConfigDirective } from "src/app/shared/directives/template-var-config.directive";

@Component({
  selector: "app-create-template",
  standalone: true,
  imports: [FormsModule, SharedModule, TemplateVarConfigDirective],
  templateUrl: "./create-template.component.html",
  styleUrl: "./create-template.component.scss",
})
export class CreateTemplateComponent {
  private fb: FormBuilder = inject(FormBuilder);
  private modalService: BsModalService = inject(BsModalService);

  breadcrumbData = [
    { label: "Communication" },
    { label: "Create Communication Template" },
  ];
  allLanguages: any[] = [
    { name: "English", code: "en" },
    { name: "Hindi", code: "hi" },
    { name: "Tamil", code: "ta" },
    { name: "Bengali", code: "bn" },
    { name: "Telugu", code: "te" },
    { name: "Marathi", code: "mr" },
    { name: "Urdu", code: "ur" },
    { name: "Gujarati", code: "gu" },
    { name: "Kannada", code: "kn" },
    { name: "Malayalam", code: "ml" },
    { name: "Odia", code: "or" },
    { name: "Punjabi", code: "pa" },
    { name: "Assamese", code: "as" },
    { name: "Maithili", code: "mai" },
    { name: "Santali", code: "sat" },
    { name: "Kashmiri", code: "ks" },
    { name: "Konkani", code: "kok" },
    { name: "Sindhi", code: "sd" },
    { name: "Sanskrit", code: "sa" },
    { name: "Manipuri", code: "mni" },
    { name: "Bodo", code: "brx" },
    { name: "Dogri", code: "doi" },
  ];
  createForm!: FormGroup;
  variables: {name: string, id: string}[] = [
    { name: "First Name", id: "FIRST_NAME" },
    { name: "Last Name", id: "LAST_NAME" },
    { name: "Overdue Amount", id: "OVERDUE_AMOUNT" },
  ];
  selectedVariable: {value: string, index: number} = { value: null, index: -1 };
  selectedLanguage: string = null;
  mapVarModalRef!: BsModalRef;
  addLangModalRef!: BsModalRef;
  @ViewChild('templateVarConfig', { static: false }) templateVarConfig!: TemplateVarConfigDirective;

  constructor() {
    this.buildCreateTemplateForm();
  }

  buildCreateTemplateForm() {
    this.createForm = this.fb.group({
      channelType: ["email"],
      templateName: [null, [Validators.required]],
      activeLanguage: [this.allLanguages[0].code],
      languages: this.buildLanguagesFormArray(),
    });
  }

  buildLanguagesFormArray(data?: any[]) {
    const formArray = new FormArray([]);
    data = data || [this.allLanguages[0]];
    data?.forEach((o) => {
      formArray.push(this.buildLanguageFormGroup(o));
    });
    return formArray;
  }

  buildLanguageFormGroup(data?: any) {
    return this.fb.group({
      languageCode: data?.code,
      languageName: data?.name,
      emailSubject: [null],
      templateBody: [null, [Validators.required]],
    });
  }

  get fValue(): any {
    return this.createForm.value;
  }

  openMapVariableModal(event: {index: number, value: string}, template: TemplateRef<any>) {
    this.selectedVariable = event;
    this.mapVarModalRef = this.modalService.show(template, {
      animated: true,
    })
  }

  assignVariable() {
    this.mapVarModalRef.hide();
    if (this.selectedVariable?.value) {
      this.templateVarConfig.onUpdateVariable(this.selectedVariable);
      this.selectedVariable = { value: null, index: -1 };
    }
  }

  updateTemplateValue(template: string, index) {
    (this.createForm.get('languages') as FormArray).at(index).patchValue({ templateBody: template });
  }

  openAddLangModal(template: TemplateRef<any>) {
    this.addLangModalRef = this.modalService.show(template, { animated: true });
  }

  addLanguage() {
    this.addLangModalRef?.hide();
    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);
    const langFormGroup = this.buildLanguageFormGroup(language);
    (this.createForm.get('languages') as FormArray).push(langFormGroup);
    this.createForm.updateValueAndValidity();
  }

  removeLanguage(index: number) {
    const languagesArray = this.createForm.get('languages') as FormArray;

    // Prevent removing the last language
    if (languagesArray.length <= 1) {
      return;
    }

    const removedLanguage = languagesArray.at(index).value;
    languagesArray.removeAt(index);

    // If the removed language was the active one, switch to the first available language
    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {
      const firstLanguage = languagesArray.at(0)?.value;
      if (firstLanguage) {
        this.createForm.patchValue({ activeLanguage: firstLanguage.languageCode });
      }
    }

    this.createForm.updateValueAndValidity();
  }
}
