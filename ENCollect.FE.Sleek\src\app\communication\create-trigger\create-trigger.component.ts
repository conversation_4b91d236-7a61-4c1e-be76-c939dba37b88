import { Component, inject, TemplateRef } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
} from "@angular/forms";
import { SharedModule } from "src/app/shared";
import { BreadcrumbComponent } from "src/app/shared/components/breadcrumb/breadcrumb.component";
import { BsModalService, BsModalRef } from "ngx-bootstrap/modal";

@Component({
  selector: "app-create-trigger",
  standalone: true,
  imports: [FormsModule, SharedModule],
  templateUrl: "./create-trigger.component.html",
  styleUrl: "./create-trigger.component.scss",
})
export class CreateTriggerComponent {
  private fb: FormBuilder = inject(FormBuilder);
  private modalService: BsModalService = inject(BsModalService);

  modalRef?: BsModalRef;
  selectedTriggerType: any = null;
  tempXValue: number = 7;

  breadcrumbData = [
    { label: "Communication" },
    { label: "Create Communication Trigger" },
  ];

  triggerTypes: any[] = [
    {
      id: 1,
      name: "On Xth day before due date",
      desc: "Send communication X days before payment is due",
      xLabel: "Days Before Due Date",
      detail: "Trigger will fire X days before the payment due date.",
      enabled: true,
    },
    {
      id: 2,
      name: "On Xth day after statement date",
      desc: "Send communication X days after statement is generated",
      xLabel: "Days After Statement Date",
      detail: "Trigger will fire X days after the statement date.",
      enabled: true,
    },
    {
      id: 3,
      name: "On X DPD (Days Past Due)",
      desc: "Send communication when account is X days past due",
      xLabel: "Days Past Due",
      detail: "Trigger will fire when an account reaches X days past due.",
      enabled: true,
    },
    {
      id: 4,
      name: "On PTP Date",
      desc: "Send communication on Promise to Pay date",
      xLabel: "",
      detail: "Trigger will fire on the date when customer promised to pay.",
      enabled: true,
    },
    {
      id: 5,
      name: "On Broken PTP",
      desc: "Re-try max for Z days when Promise to Pay is broken",
      xLabel: "",
      detail: "Trigger will fire when a customer breaks their promise to pay.",
      enabled: true,
    },
    {
      id: 6,
      name: "On Agency allocation change",
      desc: "Re-try max for Z days when account is transferred to a new agency",
      xLabel: "",
      detail:
        "Trigger will fire when an account is allocated to a different collection agency.",
      enabled: true,
    },
  ];
  createForm!: FormGroup;
  emailTemplates: any[] = [];
  smsTemplates: any[] = [];
  letterTemplates: any[] = [];

  constructor() {
    this.buildCreateTriggerForm();
  }

  buildCreateTriggerForm() {
    this.createForm = this.fb.group({
      triggerName: [null, [Validators.required]],
      status: [true],
      description: [null],
      triggerType: [1],
      xValue: [7],
      activeChannel: ["email"],
      emailTemplate: [null],
      smsTemplate: [null],
      letterTemplate: [null],
      maxOccurrences: ["1"],
    });
  }

  get fValue(): any {
    return this.createForm.value;
  }

  get activeTriggerType(): any {
    return this.triggerTypes.find((o) => o?.id === this.fValue?.triggerType);
  }

  onTriggerTypeClick(triggerType: any, template: TemplateRef<any>) {
    // If trigger type requires X value, show popup
    if (triggerType.xLabel) {
      this.selectedTriggerType = triggerType;
      this.tempXValue = this.fValue?.xValue || 7;

      const config = {
        ignoreBackdropClick: true,
        class: 'trigger-type-popup-modal'
      };
      this.modalRef = this.modalService.show(template, config);
    } else {
      // For trigger types that don't need X value, directly set the trigger type
      this.createForm.patchValue({
        triggerType: triggerType.id,
        xValue: null // Clear xValue for trigger types that don't need it
      });
    }
  }

  onPopupOkay() {
    if (this.selectedTriggerType && this.tempXValue) {
      this.createForm.patchValue({
        triggerType: this.selectedTriggerType.id,
        xValue: this.tempXValue
      });
      this.modalRef?.hide();
      this.selectedTriggerType = null;
    }
  }

  onPopupCancel() {
    this.modalRef?.hide();
    this.selectedTriggerType = null;
    this.tempXValue = 7;
  }

  getTriggerDetailText(): string {
    if (this.selectedTriggerType && this.tempXValue) {
      return this.selectedTriggerType.detail.replace('X', this.tempXValue.toString());
    }
    return '';
  }
}
