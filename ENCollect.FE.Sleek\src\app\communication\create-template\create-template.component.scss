.variable-map-legends {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  label {
    position: relative;
    &::before {
      position: absolute;
      content: "";
      width: 0.825rem;
      height: 0.825rem;
      border: solid 1px gray;
      background-color: lightgray;
      border-radius: 50%;
      left: -1.25rem;
      top: 50%;
      transform: translateY(-50%);
    }
    &.mapped::before {
      background-color: #dcfce7;
      border-color: #86efac;
    }
    &.unmapped::before {
      background-color: #fef9c3;
      border-color: #fde047;
    }
  }
}

// Language button wrapper styling
.language-button-wrapper {
  position: relative;
  display: inline-block;
}

// Language button with remove functionality
.language-btn {
  position: relative;
  padding-right: 2rem !important; // Extra space for X icon
}

// Language remove button styling
.remove-language-btn {
  position: absolute;
  top: 50%;
  right: 0.5rem;
  transform: translateY(-50%);
  color: #ef4444;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  line-height: 1;
  width: 1.2rem;
  height: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  z-index: 10;

  &:hover {
    background-color: #fef2f2;
    color: #dc2626;
    transform: translateY(-50%) scale(1.1);
  }
}

// Modal close button styling to match side drawer
::ng-deep .modal-close-btn {
  width: 1.625rem;
  height: 1.625rem;
  cursor: pointer;
  border-radius: 50%;
}
