<!--Breadcrumb starts here-->
<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"></app-breadcrumb>
  <div class="enc-card">
    <div class="card-header">
      <h3>Search Communication Task</h3>
    </div>
    <div class="card-content">
      <form name="searchCommunicationTaskForm" class="search-batch" #searchCommunicationTaskForm="ngForm" novalidate>
        <div class="row">
          <div class="col-lg-4 form-control-group col-md-6">
            <label class="form-label" for="taskName">Task Name:</label>
            <input type="text" class="form-control" name="taskName" [(ngModel)]='searchControls.taskName' id="taskName"/>
          </div>
          <div class="col-lg-4 form-control-group col-md-6">
            <label class="form-label required" for="communicationChannel">Communication Channel: </label>
            <select name="communicationChannel" class="form-select" [(ngModel)]='searchControls.communicationChannel' id="communicationChannel">
              <option value="">--Select--</option>
              <option value="sms">SMS</option>
              <option value="email">Email</option>
              <option value="letter">Letter</option>
              <option value="wa">Whats App</option>
            </select>
          </div>
          <div class="col-lg-4 form-control-group col-md-6">
            <label class="form-label" for="taskType">Task Type:</label>
            <select name="tasktype" class="form-select" [(ngModel)]='searchControls.taskType' id="taskType">
              <option value="">--Select--</option>
              <option value="automatic">Automatic</option>
              <option value="manual">Manual</option>
            </select>
          </div>
          <div class="col-lg-4 form-control-group col-md-6">
            <label class="form-label" for="createdDate">Task Created On:</label>
            <input type="text" class="form-control" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD', containerClass: 'theme-red' }" #dp="bsDatepicker" name="createdDate" bsDatepicker [outsideClick]="true" [(ngModel)]='searchControls.createdDate' id="createdDate">
          </div>
        </div>
        <div class="row">
          <div class="form-control-group col-md-6">
            <button type="submit" class="btn btn-secondary" (click)="search()" id="searchButton">Search</button>
            <span *ngIf="searchLead"><img src="assets/images/loader.gif" height="24px" width="24px" id="loader" /></span>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- search result -->
<div class="enc-card mt-4" *ngIf='totalItems>0'>
  <div class="card-header">
    <h3>Search Results</h3>
  </div>
  <div class="card-content">
      <div class="search-result">
        <div class="inside_table receipt-book">
          <div class="table-responsive" id="flip-scroll">
            <table class="table table-bordered" id="searchResultsTable">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Task name</th>
                  <th>Channel</th>
                  <th>Task Type</th>
                  <th>Created by</th>
                  <th>Created on</th>
                  <th>Execution History</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor='let data of results; let in=index'>
                  <td>{{in+1}}</td>
                  <td><a href="javscript:void(0)" [routerLink]="['/communication/view-task', data.id]" title="Click to View Task Details" id="taskNameLink{{in}}">{{data.taskName}} </a></td>
                  <ng-container *ngIf="data.channel != 'email'; else second">
                    <td>
                    <a href="javscript:void(0)" [routerLink]="['/communication/view-task', data.id]" title="Click to View Task Details" id="channelLink{{in}}">{{data.channel=='wa' ? 'Whats App' : data.channel}}</a>
                  </td>
                  </ng-container>
                  <ng-template #second>
                    <td>
                    <a href="javscript:void(0)" [routerLink]="['/communication/view-task', data.id]" title="Click to View Task Details" id="channelLink{{in}}">{{data.channel | titlecase }}</a>
                  </td>
                  </ng-template>

                  <td><a href="javscript:void(0)" [routerLink]="['/communication/view-task', data.id]" title="Click to View Task Details" id="taskTypeLink{{in}}">{{data.taskType}}</a></td>
                  <td><a href="javscript:void(0)" [routerLink]="['/communication/view-task', data.id]" title="Click to View Task Details" id="createdByLink{{in}}">{{data.createdBy}}</a></td>
                  <td><a href="javscript:void(0)" [routerLink]="['/communication/view-task', data.id]" title="Click to View Task Details" id="createdDateLink{{in}}">{{data.createdDate|date:'d/M/yyyy'}}</a></td>
                  <td><a href="javscript:void(0)" (click)="executionsHistory(data.id,executionTemplate)" id="executionHistoryLink{{in}}">{{data.executionHistory}}</a></td>
                  <td>
                    <span style="cursor:pointer;color: blue;" *ngIf="hasAccess.edit" (click)="editTask(data)" tooltip title="Edit" id="editTaskIcon{{in}}">
                      <i class="fa fa-pencil" aria-hidden="true" style="color:blue"></i>
                    </span>
                    <span style="cursor:pointer; margin-left: 10px;color: red;" *ngIf="hasAccess.delete" (click)="deleteConfirmation(data)"  tooltip title="Delete" id="deleteTaskIcon{{in}}">
                      <i class="fa fa-trash" aria-hidden="true" style="color:red"></i>
                    </span>
                    <span style="cursor:pointer; margin-left: 10px;color: orange;" *ngIf="!data.isDisabled && hasAccess.disable" (click)="enableDisableConfirmation(data.id, true)"  tooltip title="Disable" id="disableTaskIcon{{in}}">
                      <i class="fa fa-ban" aria-hidden="true" style="color:orange"></i>
                    </span>
                    <span style="cursor:pointer; margin-left: 10px;color: green;" *ngIf="data.isDisabled && hasAccess.disable" (click)="enableDisableConfirmation(data.id, false)"  tooltip title="Enable" id="enableTaskIcon{{in}}">
                      <i class="fa fa-check" aria-hidden="true" style="color:green"></i>
                    </span>
                    <span style="cursor:pointer; margin-left: 10px;color: green;"  *ngIf="data.taskType=='manual' && !data.isDisabled && hasAccess.execute" (click)="executeTask(data.id)"  tooltip title="Execute Task" id="executeTaskIcon{{in}}">
                      <i class="fa fa-play-circle" aria-hidden="true" style="color:green"></i>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="container-fluid">
          <div class="d-flex justify-content-between">
            <div class="d-flex align-items-center">
              Showing <select name="itemsPerPage" [(ngModel)]='itemsPerPage' class="form-control mx-1" (change)='changeItemsPerPage()' id="itemsPerPageSelect">
                <option *ngFor="let optionNumber of [5,10,15,20,30,40,50]" [value]="optionNumber">{{ optionNumber }}</option>
              </select> Tasks
            </div>
            <pagination [boundaryLinks]="true" [totalItems]="totalItems" (pageChanged)="pageChange($event)" [itemsPerPage]="itemsPerPage" [maxSize]="5" [(ngModel)]="currentPage" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;" id="pagination"></pagination>
          </div>
        </div>
      </div>
  </div>
  <ng-template #executionTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left" id="executionHistoryTitle">Task Execution History</h4>
      <img
        src="assets/new/svgs/side-drawer-close.svg"
        alt="Close"
        class="modal-close-btn float-right"
        (click)="modalRef?.hide()"
        id="closeExecutionHistory"
      />
    </div>
    <div class="modal-body">
      <div class="table-responsive" id="flip-scroll" style="max-height: 250px;">
        <table class="table table-bordered" id="executionHistoryTable">
          <thead>
            <tr>
              <th>Execution Date</th>
              <th>No of Communication</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor='let data of executionsDetails; let in=index'>
              <td>
                {{data.executionDate | date:'dd/MM/yyyy hh:mm:ss a'}}
              </td>
              <td><a href="javscript:void(0)" (click)="downloadFile(data)" id="downloadFileLink{{in}}">{{data.countOfCommunications}}</a></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-template>
</div>
