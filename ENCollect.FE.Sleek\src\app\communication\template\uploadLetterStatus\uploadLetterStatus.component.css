/* .heading{display:inline-block;max-width:500px;width:100%;}
.heading ul {border: 1px solid #dcdcdc;padding: 10px 10px;text-align: left;list-style: none;color: #000;margin: 10px 0;}
.mid-content .holder.auto {min-height: 100px;float: left;width: 100%;background-color: white}
h5.upload-steps {    border: 1px solid #dcdcdc;
    padding: 10px 10px 10px;
    text-align: left;
    color: #404040;
    background: #dedede;
    font-weight: 600;
    font-size: 19px;
    margin: 0;}
h5.upload-steps2{text-align: left;}
.mid-content .holder h3, .logger h3, .form-holder h3 {
    font-size: 36px;
    font-weight: 400;
    padding: 15px;
    background: none;
    border-radius: 0;
    text-shadow: 0 1px 0 #f1f1f1;
    margin-top: 30px;
    border: 1px solid #000;
}
.form-group label{text-align: left !important;float: left;width: 100%;}
span#basic-addon2 {
    position: absolute;
    top: 26px;
    right: 35px;
    background: none;
    border: inherit;
}
.border {
    float: left;
    width: 100%;
    border: 1px solid #dcdcdc;
    padding: 10px 0;
    margin: 0 0 10px 0;
}
span.label-heading {float: left;width: 100%;text-align: left;}
span.label-design {
    float: left;
    padding: 11px 0 0 0;
}
.downlaod-otr{width:100%;max-width:900px;display:inline-block;}
.welcome {font-size: 36px !important;font-weight: 500;background: none !important;color: black;border: 1px solid #dcdcdc;margin: 20px 0 13px 0 !important;border-radius: 0 !important;} */


ul {
    margin: 30px 0;
}
li {
    margin-bottom: 10px;
}

.upload-status-list li {
    margin-bottom: 0px;
}
ul.upload-status-list {
    margin: 15px 0;
}

/* Modal close button styling to match side drawer */
::ng-deep .modal-close-btn {
    width: 1.625rem;
    height: 1.625rem;
    cursor: pointer;
    border-radius: 50%;
}