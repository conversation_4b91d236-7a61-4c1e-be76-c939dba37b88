<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"> </app-breadcrumb>

  <div class="d-flex align-items-center mb-4 gap-3">
    <h2 class="title m-0">Upload Letter Status</h2>
    <button
      class="btn icon-btn btn-shade-primary p-0 h-auto w-auto d-flex align-items-center justify-content-center"
      (click)="showInstructions()"
    >
      <svg-icon src="assets/new/svgs/instruction-info.svg"> </svg-icon>
    </button>
  </div>

  <div class="enc-card">
    <div class="card-header">
      <h3>Download Letter Details</h3>
    </div>
    <div class="card-content">
      <form
        class="finone-file"
        #downloadDataForm="ngForm"
        name="downloadDataForm"
        novalidate=""
        autocomplete="off"
      >
        <div class="row">
          <div class="col-lg-4 form-control-group col-md-6">
            <label class="form-label">Communication Execution Date</label>
            <div class="input-group">
              <input id="ccmExeDate"
                type="text"
                class="form-control"
                [bsConfig]="{
                  dateInputFormat: 'YYYY-MM-DD',
                  containerClass: 'theme-red',
                  maxDate: maxDate
                }"
                #dp="bsDatepicker"
                bsDatepicker
                [outsideClick]="true"
                name="executionDate"
                [(ngModel)]="downloadControl.executionDate"
                (bsValueChange)="onValueChange($event)"
              />
            </div>
          </div>
          <div class="col-lg-4 form-control-group col-md-6">
            <label class="form-label required">Communication Task Name </label>
            <!-- <input type="text" class="form-control"> -->
            <!-- <ng-multiselect-dropdown required name="taskName"
                [placeholder]="'Select Task Name'" [data]="taskList" [(ngModel)]="selectedItems"
                [settings]="dropdownSettings" >
              </ng-multiselect-dropdown> -->
            <ng-select id="ccmmTaskName"
              class="form-ng-select"
              name="taskName"
              [items]="taskList"
              [multiple]="true"
              bindLabel="name"
              [closeOnSelect]="false"
              [(ngModel)]="selectedItems"
            >
              <!-- <ng-template
                ng-option-tmp
                let-item="item"
                let-item$="item$"
                let-index="index"
              >
                <input
                  id="item-{{ index }}"
                  type="checkbox"
                  [ngModel]="item$.selected"
                />
                {{ item.name }}
              </ng-template> -->
            </ng-select>
          </div>
          <div class="col-lg-4 form-control-group col-md-6">
            <label class="form-label required"
              >Current Communication Execution Status
            </label>
            <select
              name="communicationExecutionStatus"
              class="form-select" id="exeStatus"
              [(ngModel)]="downloadControl.communicationExecutionStatus"
              required
            >
              <option value="">--Select--</option>
              <option value="Generated">Generated</option>
              <option value="Dispatched">Dispatched</option>
              <option value="Returned">Returned</option>
            </select>
          </div>
          <div
            class="col-lg-4 form-control-group col-md-6"
            *ngIf="downloadControl.communicationExecutionStatus == 'Dispatched'"
          >
            <label class="form-label">Dispatch Date From</label>
            <div class="input-group">
              <input id="dispFrom"
                type="text"
                class="form-control"
                [bsConfig]="{
                  dateInputFormat: 'YYYY-MM-DD',
                  containerClass: 'theme-red'
                }"
                #dpFrom="bsDatepicker"
                bsDatepicker
                [outsideClick]="true"
                [(ngModel)]="downloadControl.dispatchDateFrom"
                name="dispatchDateFrom"
              />
            </div>
          </div>
          <div
            class="col-lg-4 form-control-group col-md-6"
            *ngIf="downloadControl.communicationExecutionStatus == 'Dispatched'"
          >
            <label class="form-label">Dispatch Date To</label>
            <div class="input-group">
              <input id="dipDateTo"
                type="text"
                class="form-control"
                [bsConfig]="{
                  dateInputFormat: 'YYYY-MM-DD',
                  containerClass: 'theme-red'
                }"
                #dpTo="bsDatepicker"
                bsDatepicker
                [outsideClick]="true"
                [(ngModel)]="downloadControl.dispatchDateTo"
                name="dispatchDateTo"
              />
            </div>
          </div>
          <div class="form-group col-md-12 text-left">
            <button id="btn-download-file"
              type="submit"
              class="btn btn-primary cr"
              (click)="downloadFile()"
              [disabled]="
                !downloadControl.communicationExecutionStatus ||
                selectedItems.length == 0
              "
            >
              Download File
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-md-6">
      <div class="enc-card h-100">
        <div class="card-header">
          <h3>Download Template</h3>
        </div>
        <div class="card-content">
          <div class="row">
            <div class="col-12">
              <div class="form-control-group">
                <label class="form-label">Select Template for Upload</label>
                <select
                  name="template"
                  [(ngModel)]="uploadControls.template"
                  class="form-select" id="tempUpload"
                >
                  <option value="">--Select--</option>
                  <option value="dispatched">
                    Template for Dispatched Letters
                  </option>
                  <option value="returned">
                    Template for Returned Letters
                  </option>
                </select>
              </div>
            </div>

            <div class="col-md-12">
              <button id="btn-down"
                class="btn btn-secondary"
                [disabled]="true"
                *ngIf="!uploadControls.template"
              >
                Download Template
              </button>
              <a
                *ngIf="uploadControls.template == 'dispatched'"
                href="assets/templates/TemplateDispatchUpload.xlsx"
                class="btn btn-secondary"
                download
                >Download Template</a
              >
              <a
                *ngIf="uploadControls.template == 'returned'"
                href="assets/templates/TemplateReturnsUpload.xlsx"
                class="btn btn-secondary"
                download
                >Download Template</a
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="enc-card h-100">
        <div class="card-header">
          <h3>Upload File</h3>
        </div>
        <div class="card-content">
          <div class="row">
            <div class="col-12">
              <div class="form-control-group">
                <label class="form-label">Select Letter Type</label>
                <div class="form-radio-group with-box">
                  <label>
                    <input id="dispLetters"
                      type="radio"
                      value="dispatchedLetters"
                      name="fileType"
                      [(ngModel)]="uploadControls.fileType"
                    />Dispatched Letters
                  </label>
                  <label>
                    <input id="retLetters"
                      type="radio"
                      value="returnedLetters"
                      name="fileType"
                      [(ngModel)]="uploadControls.fileType"
                    />Returned Letters
                  </label>
                </div>
              </div>
              <div class="form-control-group">
                <label class="form-label">Select File</label>
                <input
                  type="file" id="select-file"
                  class="form-control"
                  name="attachedFile"
                  (change)="fileUploadConfirmation($event, confirmation)"
                  [(ngModel)]="uploadControls.fileName"
                  accept=".xls,.xlsx"
                  #fileUploader
                />
              </div>
              <div>
                <button id="btn-upload" class="btn btn-secondary mw-150px">Upload File</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="enc-card" *ngIf="false">
    <div class="card-content">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="holder auto mid-content">
              <div class="row">
                <div class="card-header">
                  <h3>Upload Letter status Update Steps</h3>
                </div>
                <div class="col-sm-12">
                  <div>
                    <ul class="upload-status-list">
                      <li>
                        <b>Step 1:</b> Select 'Upload Letter Status' under
                        'Communications' tab
                      </li>
                      <li>
                        <b>Step 2:</b> Search for letters using the given
                        filters
                      </li>
                      <li>
                        <b>Step 3:</b> Click 'Download' button to download the
                        file with the list of Letters satisfying the search
                        criteria.
                      </li>
                      <li>
                        <b>Step 4:</b> In the Downloaded file,update the details
                        as per the template of the file
                      </li>
                      <li>
                        <b>Step 5:</b> Do not change the format of the file and
                        do not write anything beyond the given columns.
                      </li>
                      <li>
                        <b>Step 6:</b> Save the updated file and use upload
                        button to upload the file so as to update the
                        communication status of the letters.
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="holder auto">
          <div class="row">
            <div class="col-md-12">
              <h3>Download Letter Details</h3>
            </div>
            <div class="col-md-12">
              <div class="downlaod-otr">
                <form
                  class="finone-file"
                  #downloadDataForm="ngForm"
                  name="downloadDataForm"
                  novalidate=""
                  autocomplete="off"
                >
                  <div class="col-md-12">
                    <div class="row">
                      <div class="col-lg-4 form-control-group col-md-6">
                        <label class="form-label"
                          >Communication Execution Date</label
                        >
                        <div class="input-group">
                          <input id="ccm-exeDate"
                            type="text"
                            class="form-control"
                            [bsConfig]="{
                              dateInputFormat: 'YYYY-MM-DD',
                              containerClass: 'theme-red'
                            }"
                            #dp="bsDatepicker"
                            bsDatepicker
                            [outsideClick]="true"
                            name="executionDate"
                            [(ngModel)]="downloadControl.executionDate"
                            (bsValueChange)="onValueChange($event)"
                          />
                          <div
                            class="input-group-append"
                            (click)="dp.toggle()"
                            [attr.aria-expanded]="dp.isOpen"
                          >
                            <span class="input-group-text">
                              <img
                                src="assets/images/calendar.png"
                                style="width: 1.5rem; cursor: pointer"
                              />
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-4 form-control-group col-md-6">
                        <label class="form-label required"
                          >Communication Task Name
                        </label>
                        <!-- <input type="text" class="form-control"> -->
                        <!-- <ng-multiselect-dropdown required name="taskName"
                          [placeholder]="'Select Task Name'" [data]="taskList" [(ngModel)]="selectedItems"
                          [settings]="dropdownSettings" >
                        </ng-multiselect-dropdown> -->
                        <ng-select
                          class="form-select"
                          name="taskName" id="ccm-selecItem"
                          [items]="taskList"
                          [multiple]="true"
                          bindLabel="name"
                          [closeOnSelect]="false"
                          [(ngModel)]="selectedItems"
                        >
                          <ng-template
                            ng-option-tmp
                            let-item="item"
                            let-item$="item$"
                            let-index="index"
                          >
                            <input
                              id="item-{{ index }}"
                              type="checkbox"
                              [ngModel]="item$.selected"
                            />
                            {{ item.name }}
                          </ng-template>
                        </ng-select>
                      </div>
                      <div class="col-lg-4 form-control-group col-md-6">
                        <label class="form-label required"
                          >Current Communication Execution Status
                        </label>
                        <select id="ccm-exestatus"
                          name="communicationExecutionStatus"
                          class="form-select"
                          [(ngModel)]="
                            downloadControl.communicationExecutionStatus
                          "
                          required
                        >
                          <option value="">--Select--</option>
                          <option value="Generated">Generated</option>
                          <option value="Dispatched">Dispatched</option>
                          <option value="Returned">Returned</option>
                        </select>
                      </div>
                      <div
                        class="col-lg-4 form-control-group col-md-6"
                        *ngIf="
                          downloadControl.communicationExecutionStatus ==
                          'Dispatched'
                        "
                      >
                        <label class="form-label">Dispatch Date From</label>
                        <div class="input-group">
                          <input id="ccm-dispatchDate"
                            type="text"
                            class="form-control"
                            [bsConfig]="{
                              dateInputFormat: 'YYYY-MM-DD',
                              containerClass: 'theme-red'
                            }"
                            #dpFrom="bsDatepicker"
                            bsDatepicker
                            [outsideClick]="true"
                            [(ngModel)]="downloadControl.dispatchDateFrom"
                            name="dispatchDateFrom"
                          />
                          <div
                            class="input-group-append"
                            (click)="dpFrom.toggle()"
                            [attr.aria-expanded]="dpFrom.isOpen"
                          >
                            <span class="input-group-text">
                              <img
                                src="assets/images/calendar.png"
                                style="width: 1.5rem; cursor: pointer"
                              />
                            </span>
                          </div>
                        </div>
                      </div>
                      <div
                        class="col-lg-4 form-control-group col-md-6"
                        *ngIf="
                          downloadControl.communicationExecutionStatus ==
                          'Dispatched'
                        "
                      >
                        <label class="form-label">Dispatch Date To</label>
                        <div class="input-group">
                          <input id="disp-to"
                            type="text"
                            class="form-control"
                            [bsConfig]="{
                              dateInputFormat: 'YYYY-MM-DD',
                              containerClass: 'theme-red'
                            }"
                            #dpTo="bsDatepicker"
                            bsDatepicker
                            [outsideClick]="true"
                            [(ngModel)]="downloadControl.dispatchDateTo"
                            name="dispatchDateTo"
                          />
                          <div
                            class="input-group-append"
                            (click)="dpTo.toggle()"
                            [attr.aria-expanded]="dpTo.isOpen"
                          >
                            <span class="input-group-text">
                              <img
                                src="assets/images/calendar.png"
                                style="width: 1.5rem; cursor: pointer"
                              />
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="form-group col-md-12 text-left">
                        <button id="btn-download-file"
                          type="submit"
                          class="btn btn-primary cr"
                          (click)="downloadFile()"
                          [disabled]="
                            !downloadControl.communicationExecutionStatus ||
                            selectedItems.length == 0
                          "
                        >
                          Download File
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div class="holder auto">
          <div class="row">
            <div class="col-md-12">
              <div class="holder auto">
                <h3>Update Letter Status</h3>
                <form
                  class="search-batch finone-file"
                  #allocationFileUpload="ngForm"
                  name="allocationFileUpload"
                  novalidate=""
                  autocomplete="off"
                >
                  <div class="row allocation_file">
                    <div class="col-md-6" style="text-align: left">
                      <div class="col-md-12 form-group">
                        <label>Select Template for Upload</label>
                        <select id="tempType"
                          name="template"
                          [(ngModel)]="uploadControls.template"
                          class="form-control"
                        >
                          <option value="">--Select--</option>
                          <option value="dispatched">
                            Template for Dispatched Letters
                          </option>
                          <option value="returned">
                            Template for Returned Letters
                          </option>
                        </select>
                      </div>
                      <div
                        class="col-md-12 form-group"
                        *ngIf="uploadControls.template == 'dispatched'"
                      >
                        <label> </label>
                        <br />
                        <a
                          href="assets/templates/TemplateDispatchUpload.xlsx"
                          class="btn btn-primary cr"
                          download
                          >Download Template</a
                        >
                      </div>
                      <div
                        class="col-md-12 form-group"
                        *ngIf="uploadControls.template == 'returned'"
                      >
                        <label> </label>
                        <br />
                        <a
                          href="assets/templates/TemplateReturnsUpload.xlsx"
                          class="btn btn-primary cr"
                          download
                          >Download Template</a
                        >
                      </div>
                    </div>
                    <div class="col-md-6" style="text-align: left">
                      <div class="col-md-12 col-sm-12">
                        <label>Select Letter Type</label>
                        <div class="form-group" style="padding-top: 9px">
                          <label class="radio-inline">
                            <input id="dispLetters"
                              type="radio"
                              value="dispatchedLetters"
                              name="fileType"
                              [(ngModel)]="uploadControls.fileType"
                            />Dispatched Letters
                          </label>
                          <label class="radio-inline">
                            <input id="returnLetters"
                              type="radio"
                              value="returnedLetters"
                              name="fileType"
                              [(ngModel)]="uploadControls.fileType"
                            />Returned Letters
                          </label>
                        </div>
                      </div>
                      <div class="col-md-12 col-sm-12">
                        <br />
                        <label>Upload Letter Status</label>
                        <div
                          class="fileupload fileupload-new"
                          data-provides="fileupload"
                        >
                          <div class="input-group">
                            <div class="input-group-btn">
                              <a
                                class="btn btn-primary btn-file"
                                href="javascript:void(0)"
                              >
                                <span class="fileupload-new">Upload</span>
                                <input id="upload"
                                  type="file"
                                  class="file-input"
                                  name="attachedFile"
                                  (change)="
                                    fileUploadConfirmation($event, confirmation)
                                  "
                                  [(ngModel)]="uploadControls.fileName"
                                  accept=".xls,.xlsx"
                                  #fileUploader
                                />
                              </a>
                              <a
                                href="#" id="btn-remove"
                                class="btn btn-primary fileupload-exists"
                                data-dismiss="fileupload"
                                >Remove</a
                              >
                            </div>
                            <div class="form-control uneditable-input">
                              <span class="fileupload-preview">
                                {{ uploadControls.fileName }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</div>
<ng-template
  #confirmation
  let-c="close"
  let-d="dismiss"
  class="modal-dialognew"
>
  <div class="modal-header">
    <!-- <h4 class="modal-title">Modal title</h4> -->
    <img
      src="assets/new/svgs/side-drawer-close.svg"
      alt="Close"
      class="modal-close-btn"
      (click)="modalRef?.hide()"
    />
  </div>
  <div class="modal-body">
    <p>
      Are you sure you want to upload this file
      <b>{{ uploadControls.fileName }}</b
      >?
    </p>
  </div>
  <div class="modal-footer">
    <button id="btn-ok"
      type="button"
      class="btn btn-success"
      (click)="fIleUpload()"
      [disabled]="onceClicked"
    >
      Ok
    </button>
    <button type="button" id="btn-close" class="btn btn-danger" (click)="modalRef?.hide()">
      Close
    </button>
  </div>
</ng-template>
