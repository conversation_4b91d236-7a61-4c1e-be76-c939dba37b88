{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./search-templates.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./search-templates.component.scss?ngResource\";\nimport { Component } from \"@angular/core\";\nimport { FormsModule } from \"@angular/forms\";\nimport { SharedModule } from \"src/app/shared\";\nlet SearchTemplatesComponent = class SearchTemplatesComponent {\n  constructor() {\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Search Communication Templates\"\n    }];\n    this.hasAccess = {\n      create: true,\n      view: true,\n      edit: true\n    };\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalItems = 5;\n    this.templates = [{\n      id: \"1\",\n      templateName: \"Payment Reminder Template\",\n      description: \"Email Template for Payment Reminder\",\n      channel: \"Email\",\n      languages: [\"en\"],\n      createdBy: \"<PERSON>\",\n      createdOn: \"2024-01-15\",\n      countOfTriggers: 3,\n      status: \"Active\"\n    }, {\n      id: \"2\",\n      templateName: \"Welcome Email Template\",\n      description: \"Email Template for New User Welcome\",\n      channel: \"Email\",\n      languages: [\"en\", \"es\"],\n      createdBy: \"Jane Smith\",\n      createdOn: \"2024-01-10\",\n      countOfTriggers: 5,\n      status: \"Inactive\"\n    }, {\n      id: \"3\",\n      templateName: \"SMS Notification Template\",\n      description: \"SMS Template for Payment Notifications\",\n      channel: \"SMS\",\n      languages: [\"en\"],\n      createdBy: \"Mike Johnson\",\n      createdOn: \"2024-01-20\",\n      countOfTriggers: 2,\n      status: \"Active\"\n    }, {\n      id: \"4\",\n      templateName: \"Account Suspension Notice\",\n      description: \"Email Template for Account Suspension\",\n      channel: \"Email\",\n      languages: [\"en\", \"fr\"],\n      createdBy: \"Sarah Wilson\",\n      createdOn: \"2024-01-05\",\n      countOfTriggers: 1,\n      status: \"Inactive\"\n    }, {\n      id: \"5\",\n      templateName: \"Monthly Statement Template\",\n      description: \"Email Template for Monthly Statements\",\n      channel: \"Email\",\n      languages: [\"en\"],\n      createdBy: \"David Brown\",\n      createdOn: \"2024-01-25\",\n      countOfTriggers: 8,\n      status: \"Active\"\n    }];\n    // Filter\n    this.filter = {\n      searchTerm: \"\",\n      channel: null\n    };\n    this.isLoading = false;\n  }\n};\nSearchTemplatesComponent = __decorate([Component({\n  selector: \"app-search-templates\",\n  standalone: true,\n  imports: [FormsModule, SharedModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SearchTemplatesComponent);\nexport { SearchTemplatesComponent };", "map": {"version": 3, "names": ["Component", "FormsModule", "SharedModule", "SearchTemplatesComponent", "constructor", "breadcrumbData", "label", "hasAccess", "create", "view", "edit", "currentPage", "itemsPerPage", "totalItems", "templates", "id", "templateName", "description", "channel", "languages", "created<PERSON>y", "createdOn", "countOfTriggers", "status", "filter", "searchTerm", "isLoading", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\search-templates\\search-templates.component.ts"], "sourcesContent": ["import { Component } from \"@angular/core\";\r\nimport { FormsModule } from \"@angular/forms\";\r\nimport { SharedModule } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\n\r\n@Component({\r\n  selector: \"app-search-templates\",\r\n  standalone: true,\r\n  imports: [FormsModule, SharedModule],\r\n  templateUrl: \"./search-templates.component.html\",\r\n  styleUrl: \"./search-templates.component.scss\",\r\n})\r\nexport class SearchTemplatesComponent {\r\n  breadcrumbData = [\r\n    { label: \"Communication\" },\r\n    { label: \"Search Communication Templates\" },\r\n  ];\r\n  hasAccess = {\r\n    create: true,\r\n    view: true,\r\n    edit: true,\r\n  };\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  itemsPerPage = 10;\r\n  totalItems = 5;\r\n  templates = [\r\n    {\r\n      id: \"1\",\r\n      templateName: \"Payment Reminder Template\",\r\n      description: \"Email Template for Payment Reminder\",\r\n      channel: \"Email\",\r\n      languages: [\"en\"],\r\n      createdBy: \"<PERSON>\",\r\n      createdOn: \"2024-01-15\",\r\n      countOfTriggers: 3,\r\n      status: \"Active\"\r\n    },\r\n    {\r\n      id: \"2\",\r\n      templateName: \"Welcome Email Template\",\r\n      description: \"Email Template for New User Welcome\",\r\n      channel: \"Email\",\r\n      languages: [\"en\", \"es\"],\r\n      createdBy: \"Jane Smith\",\r\n      createdOn: \"2024-01-10\",\r\n      countOfTriggers: 5,\r\n      status: \"Inactive\"\r\n    },\r\n    {\r\n      id: \"3\",\r\n      templateName: \"SMS Notification Template\",\r\n      description: \"SMS Template for Payment Notifications\",\r\n      channel: \"SMS\",\r\n      languages: [\"en\"],\r\n      createdBy: \"Mike Johnson\",\r\n      createdOn: \"2024-01-20\",\r\n      countOfTriggers: 2,\r\n      status: \"Active\"\r\n    },\r\n    {\r\n      id: \"4\",\r\n      templateName: \"Account Suspension Notice\",\r\n      description: \"Email Template for Account Suspension\",\r\n      channel: \"Email\",\r\n      languages: [\"en\", \"fr\"],\r\n      createdBy: \"Sarah Wilson\",\r\n      createdOn: \"2024-01-05\",\r\n      countOfTriggers: 1,\r\n      status: \"Inactive\"\r\n    },\r\n    {\r\n      id: \"5\",\r\n      templateName: \"Monthly Statement Template\",\r\n      description: \"Email Template for Monthly Statements\",\r\n      channel: \"Email\",\r\n      languages: [\"en\"],\r\n      createdBy: \"David Brown\",\r\n      createdOn: \"2024-01-25\",\r\n      countOfTriggers: 8,\r\n      status: \"Active\"\r\n    },\r\n  ];\r\n\r\n  // Filter\r\n  filter = {\r\n    searchTerm: \"\",\r\n    channel: null,\r\n  };\r\n\r\n  isLoading = false;\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAUtC,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAA9BC,YAAA;IACL,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAAgC,CAAE,CAC5C;IACD,KAAAC,SAAS,GAAG;MACVC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;KACP;IAED;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,SAAS,GAAG,CACV;MACEC,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,qCAAqC;MAClDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,CAAC;MACjBC,SAAS,EAAE,UAAU;MACrBC,SAAS,EAAE,YAAY;MACvBC,eAAe,EAAE,CAAC;MAClBC,MAAM,EAAE;KACT,EACD;MACER,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,wBAAwB;MACtCC,WAAW,EAAE,qCAAqC;MAClDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACvBC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE,YAAY;MACvBC,eAAe,EAAE,CAAC;MAClBC,MAAM,EAAE;KACT,EACD;MACER,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,wCAAwC;MACrDC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,CAAC,IAAI,CAAC;MACjBC,SAAS,EAAE,cAAc;MACzBC,SAAS,EAAE,YAAY;MACvBC,eAAe,EAAE,CAAC;MAClBC,MAAM,EAAE;KACT,EACD;MACER,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,2BAA2B;MACzCC,WAAW,EAAE,uCAAuC;MACpDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACvBC,SAAS,EAAE,cAAc;MACzBC,SAAS,EAAE,YAAY;MACvBC,eAAe,EAAE,CAAC;MAClBC,MAAM,EAAE;KACT,EACD;MACER,EAAE,EAAE,GAAG;MACPC,YAAY,EAAE,4BAA4B;MAC1CC,WAAW,EAAE,uCAAuC;MACpDC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,CAAC,IAAI,CAAC;MACjBC,SAAS,EAAE,aAAa;MACxBC,SAAS,EAAE,YAAY;MACvBC,eAAe,EAAE,CAAC;MAClBC,MAAM,EAAE;KACT,CACF;IAED;IACA,KAAAC,MAAM,GAAG;MACPC,UAAU,EAAE,EAAE;MACdP,OAAO,EAAE;KACV;IAED,KAAAQ,SAAS,GAAG,KAAK;EACnB;CAAC;AAhFYvB,wBAAwB,GAAAwB,UAAA,EAPpC3B,SAAS,CAAC;EACT4B,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC7B,WAAW,EAAEC,YAAY,CAAC;EACpC6B,QAAA,EAAAC,oBAAgD;;CAEjD,CAAC,C,EACW7B,wBAAwB,CAgFpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}