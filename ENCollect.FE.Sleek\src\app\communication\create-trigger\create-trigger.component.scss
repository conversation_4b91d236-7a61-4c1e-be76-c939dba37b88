@import "../../../styles/variables";

.trigger-type-btn-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  .trigger-type-btn {
    border: solid 1px $input-border-color;
    border-radius: 6px;
    background-color: $white-color;
    text-align: left;
    padding: 1rem 5rem 1rem 1.5rem;
    position: relative;

    &.active,
    &:hover {
      background-color: rgba-color($primary-theme-color, 0.05);
    }

    &.active {
      border-color: $primary-theme-color;
      svg-icon.active-check-icon {
        display: block;
      }
    }

    &:disabled {
      pointer-events: none;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .trigger-name {
      color: $primary-text-color;
      font-size: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;

      svg-icon,
      svg-icon svg {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
      }
    }

    .trigger-desc {
      color: rgba-color($primary-text-color, 0.6);
      font-size: 0.875rem;
    }

    svg-icon.active-check-icon {
      display: none;
      position: absolute;
      right: 1.5rem;
      top: 50%;
      transform: translateY(-50%);
      height: 2rem;
      width: 2rem;
      svg {
        height: 2rem;
        width: 2rem;
        path {
          fill: $primary-theme-color;
        }
      }
    }
  }
}

.alert.alert-primary {
  border-radius: 6px;
  border-width: 0;
  background-color: rgba-color($primary-theme-color, 0.1);
}

// Trigger Type Popup Styles
:host ::ng-deep .trigger-type-popup-modal {
  .modal-dialog {
    max-width: 500px;
    margin: 10% auto;
  }

  .modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

.trigger-type-popup {
  padding: 2rem;
  text-align: center;

  .popup-content {
    .popup-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: $primary-text-color;
      margin-bottom: 0.75rem;
      line-height: 1.4;
    }

    .popup-subtitle {
      font-size: 1rem;
      color: rgba-color($primary-text-color, 0.7);
      margin-bottom: 1.5rem;
    }

    .input-section {
      margin-bottom: 2rem;

      .input-group {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin-bottom: 1rem;

        .x-value-input {
          width: 60px;
          text-align: center;
          font-size: 1rem;
          font-weight: 600;
          border: 2px solid $input-border-color;
          border-radius: 6px;
          padding: 0.375rem 0.25rem;

          &:focus {
            border-color: $primary-theme-color;
            box-shadow: 0 0 0 0.2rem rgba-color($primary-theme-color, 0.25);
          }
        }

        .input-label {
          font-size: 1rem;
          font-weight: 500;
          color: $primary-text-color;
        }
      }

      .trigger-description {
        font-size: 0.875rem;
        color: rgba-color($primary-text-color, 0.6);
        margin: 0;
        padding: 0.75rem 1rem;
        background-color: rgba-color($primary-theme-color, 0.05);
        border-radius: 6px;
        border-left: 3px solid $primary-theme-color;
      }
    }

    .popup-buttons {
      display: flex;
      flex-direction: column;
      gap: 1.25rem;
      width: 100%;
    }
  }
}
