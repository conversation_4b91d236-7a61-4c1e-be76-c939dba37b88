{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-template.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-template.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from \"@angular/core\";\nimport { FormArray, FormBuilder, FormsModule, Validators } from \"@angular/forms\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nimport { SharedModule } from \"src/app/shared\";\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\nlet CreateTemplateComponent = class CreateTemplateComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.modalService = inject(BsModalService);\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Create Communication Template\"\n    }];\n    this.allLanguages = [{\n      name: \"English\",\n      code: \"en\"\n    }, {\n      name: \"Hindi\",\n      code: \"hi\"\n    }, {\n      name: \"Tamil\",\n      code: \"ta\"\n    }, {\n      name: \"Bengali\",\n      code: \"bn\"\n    }, {\n      name: \"Telugu\",\n      code: \"te\"\n    }, {\n      name: \"Marathi\",\n      code: \"mr\"\n    }, {\n      name: \"Urdu\",\n      code: \"ur\"\n    }, {\n      name: \"Gujarati\",\n      code: \"gu\"\n    }, {\n      name: \"Kannada\",\n      code: \"kn\"\n    }, {\n      name: \"Malayalam\",\n      code: \"ml\"\n    }, {\n      name: \"Odia\",\n      code: \"or\"\n    }, {\n      name: \"Punjabi\",\n      code: \"pa\"\n    }, {\n      name: \"Assamese\",\n      code: \"as\"\n    }, {\n      name: \"Maithili\",\n      code: \"mai\"\n    }, {\n      name: \"Santali\",\n      code: \"sat\"\n    }, {\n      name: \"Kashmiri\",\n      code: \"ks\"\n    }, {\n      name: \"Konkani\",\n      code: \"kok\"\n    }, {\n      name: \"Sindhi\",\n      code: \"sd\"\n    }, {\n      name: \"Sanskrit\",\n      code: \"sa\"\n    }, {\n      name: \"Manipuri\",\n      code: \"mni\"\n    }, {\n      name: \"Bodo\",\n      code: \"brx\"\n    }, {\n      name: \"Dogri\",\n      code: \"doi\"\n    }];\n    this.variables = [{\n      name: \"First Name\",\n      id: \"FIRST_NAME\"\n    }, {\n      name: \"Last Name\",\n      id: \"LAST_NAME\"\n    }, {\n      name: \"Overdue Amount\",\n      id: \"OVERDUE_AMOUNT\"\n    }];\n    this.selectedVariable = {\n      value: null,\n      index: -1\n    };\n    this.selectedLanguage = null;\n    this.buildCreateTemplateForm();\n  }\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\"],\n      templateName: [null, [Validators.required]],\n      activeLanguage: [this.allLanguages[0].code],\n      languages: this.buildLanguagesFormArray()\n    });\n  }\n  buildLanguagesFormArray(data) {\n    const formArray = new FormArray([]);\n    data = data || [this.allLanguages[0]];\n    data?.forEach(o => {\n      formArray.push(this.buildLanguageFormGroup(o));\n    });\n    return formArray;\n  }\n  buildLanguageFormGroup(data) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null, [Validators.required]]\n    });\n  }\n  get fValue() {\n    return this.createForm.value;\n  }\n  openMapVariableModal(event, template) {\n    this.selectedVariable = event;\n    this.mapVarModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  assignVariable() {\n    this.mapVarModalRef.hide();\n    if (this.selectedVariable?.value) {\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\n      this.selectedVariable = {\n        value: null,\n        index: -1\n      };\n    }\n  }\n  updateTemplateValue(template, index) {\n    this.createForm.get('languages').at(index).patchValue({\n      templateBody: template\n    });\n  }\n  openAddLangModal(template) {\n    this.addLangModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  addLanguage() {\n    this.addLangModalRef?.hide();\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\n    const langFormGroup = this.buildLanguageFormGroup(language);\n    this.createForm.get('languages').push(langFormGroup);\n    this.createForm.updateValueAndValidity();\n  }\n  removeLanguage(index) {\n    const languagesArray = this.createForm.get('languages');\n    // Don't allow removing if only one language remains\n    if (languagesArray.length <= 1) {\n      return;\n    }\n    const removedLanguageCode = languagesArray.at(index).get('languageCode')?.value;\n    // Remove the language from the form array\n    languagesArray.removeAt(index);\n    // If the removed language was the active one, set the first available language as active\n    if (this.createForm.get('activeLanguage')?.value === removedLanguageCode) {\n      const firstLanguageCode = languagesArray.at(0)?.get('languageCode')?.value;\n      this.createForm.patchValue({\n        activeLanguage: firstLanguageCode\n      });\n    }\n    this.createForm.updateValueAndValidity();\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      templateVarConfig: [{\n        type: ViewChild,\n        args: ['templateVarConfig', {\n          static: false\n        }]\n      }]\n    };\n  }\n};\nCreateTemplateComponent = __decorate([Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [FormsModule, SharedModule, TemplateVarConfigDirective],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTemplateComponent);\nexport { CreateTemplateComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormArray", "FormBuilder", "FormsModule", "Validators", "BsModalService", "SharedModule", "TemplateVarConfigDirective", "CreateTemplateComponent", "constructor", "fb", "modalService", "breadcrumbData", "label", "allLanguages", "name", "code", "variables", "id", "selectedVariable", "value", "index", "selectedLanguage", "buildCreateTemplateForm", "createForm", "group", "channelType", "templateName", "required", "activeLanguage", "languages", "buildLanguagesFormArray", "data", "formArray", "for<PERSON>ach", "o", "push", "buildLanguageFormGroup", "languageCode", "languageName", "emailSubject", "templateBody", "fValue", "openMapVariableModal", "event", "template", "mapVarModalRef", "show", "animated", "assignVariable", "hide", "templateVarConfig", "onUpdateVariable", "updateTemplateValue", "get", "at", "patchValue", "openAddLangModal", "addLangModalRef", "addLanguage", "language", "find", "langFormGroup", "updateValueAndValidity", "removeLanguage", "languagesArray", "length", "removedLanguageCode", "removeAt", "firstLanguageCode", "args", "static", "__decorate", "selector", "standalone", "imports", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\create-template.component.ts"], "sourcesContent": ["import { Component, inject, TemplateRef, ViewChild } from \"@angular/core\";\r\nimport {\r\n  FormArray,\r\n  FormBuilder,\r\n  FormGroup,\r\n  FormsModule,\r\n  Validators,\r\n} from \"@angular/forms\";\r\nimport { BsModalRef, BsModalService } from \"ngx-bootstrap/modal\";\r\nimport { SharedModule } from \"src/app/shared\";\r\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\r\n\r\n@Component({\r\n  selector: \"app-create-template\",\r\n  standalone: true,\r\n  imports: [FormsModule, SharedModule, TemplateVarConfigDirective],\r\n  templateUrl: \"./create-template.component.html\",\r\n  styleUrl: \"./create-template.component.scss\",\r\n})\r\nexport class CreateTemplateComponent {\r\n  private fb: FormBuilder = inject(FormBuilder);\r\n  private modalService: BsModalService = inject(BsModalService);\r\n\r\n  breadcrumbData = [\r\n    { label: \"Communication\" },\r\n    { label: \"Create Communication Template\" },\r\n  ];\r\n  allLanguages: any[] = [\r\n    { name: \"English\", code: \"en\" },\r\n    { name: \"Hindi\", code: \"hi\" },\r\n    { name: \"Tamil\", code: \"ta\" },\r\n    { name: \"Bengali\", code: \"bn\" },\r\n    { name: \"Telugu\", code: \"te\" },\r\n    { name: \"Marathi\", code: \"mr\" },\r\n    { name: \"Urdu\", code: \"ur\" },\r\n    { name: \"Gujarati\", code: \"gu\" },\r\n    { name: \"Kannada\", code: \"kn\" },\r\n    { name: \"Malayalam\", code: \"ml\" },\r\n    { name: \"Odia\", code: \"or\" },\r\n    { name: \"Punjabi\", code: \"pa\" },\r\n    { name: \"Assamese\", code: \"as\" },\r\n    { name: \"Maithili\", code: \"mai\" },\r\n    { name: \"Santali\", code: \"sat\" },\r\n    { name: \"Kashmiri\", code: \"ks\" },\r\n    { name: \"Konkani\", code: \"kok\" },\r\n    { name: \"Sindhi\", code: \"sd\" },\r\n    { name: \"Sanskrit\", code: \"sa\" },\r\n    { name: \"Manipuri\", code: \"mni\" },\r\n    { name: \"Bodo\", code: \"brx\" },\r\n    { name: \"Dogri\", code: \"doi\" },\r\n  ];\r\n  createForm!: FormGroup;\r\n  variables: {name: string, id: string}[] = [\r\n    { name: \"First Name\", id: \"FIRST_NAME\" },\r\n    { name: \"Last Name\", id: \"LAST_NAME\" },\r\n    { name: \"Overdue Amount\", id: \"OVERDUE_AMOUNT\" },\r\n  ];\r\n  selectedVariable: {value: string, index: number} = { value: null, index: -1 };\r\n  selectedLanguage: string = null;\r\n  mapVarModalRef!: BsModalRef;\r\n  addLangModalRef!: BsModalRef;\r\n  @ViewChild('templateVarConfig', { static: false }) templateVarConfig!: TemplateVarConfigDirective;\r\n\r\n  constructor() {\r\n    this.buildCreateTemplateForm();\r\n  }\r\n\r\n  buildCreateTemplateForm() {\r\n    this.createForm = this.fb.group({\r\n      channelType: [\"email\"],\r\n      templateName: [null, [Validators.required]],\r\n      activeLanguage: [this.allLanguages[0].code],\r\n      languages: this.buildLanguagesFormArray(),\r\n    });\r\n  }\r\n\r\n  buildLanguagesFormArray(data?: any[]) {\r\n    const formArray = new FormArray([]);\r\n    data = data || [this.allLanguages[0]];\r\n    data?.forEach((o) => {\r\n      formArray.push(this.buildLanguageFormGroup(o));\r\n    });\r\n    return formArray;\r\n  }\r\n\r\n  buildLanguageFormGroup(data?: any) {\r\n    return this.fb.group({\r\n      languageCode: data?.code,\r\n      languageName: data?.name,\r\n      emailSubject: [null],\r\n      templateBody: [null, [Validators.required]],\r\n    });\r\n  }\r\n\r\n  get fValue(): any {\r\n    return this.createForm.value;\r\n  }\r\n\r\n  openMapVariableModal(event: {index: number, value: string}, template: TemplateRef<any>) {\r\n    this.selectedVariable = event;\r\n    this.mapVarModalRef = this.modalService.show(template, {\r\n      animated: true,\r\n    })\r\n  }\r\n\r\n  assignVariable() {\r\n    this.mapVarModalRef.hide();\r\n    if (this.selectedVariable?.value) {\r\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\r\n      this.selectedVariable = { value: null, index: -1 };\r\n    }\r\n  }\r\n\r\n  updateTemplateValue(template: string, index) {\r\n    (this.createForm.get('languages') as FormArray).at(index).patchValue({ templateBody: template });\r\n  }\r\n\r\n  openAddLangModal(template: TemplateRef<any>) {\r\n    this.addLangModalRef = this.modalService.show(template, { animated: true });\r\n  }\r\n\r\n  addLanguage() {\r\n    this.addLangModalRef?.hide();\r\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\r\n    const langFormGroup = this.buildLanguageFormGroup(language);\r\n    (this.createForm.get('languages') as FormArray).push(langFormGroup);\r\n    this.createForm.updateValueAndValidity();\r\n  }\r\n\r\n  removeLanguage(index: number) {\r\n    const languagesArray = this.createForm.get('languages') as FormArray;\r\n\r\n    // Don't allow removing if only one language remains\r\n    if (languagesArray.length <= 1) {\r\n      return;\r\n    }\r\n\r\n    const removedLanguageCode = languagesArray.at(index).get('languageCode')?.value;\r\n\r\n    // Remove the language from the form array\r\n    languagesArray.removeAt(index);\r\n\r\n    // If the removed language was the active one, set the first available language as active\r\n    if (this.createForm.get('activeLanguage')?.value === removedLanguageCode) {\r\n      const firstLanguageCode = languagesArray.at(0)?.get('languageCode')?.value;\r\n      this.createForm.patchValue({ activeLanguage: firstLanguageCode });\r\n    }\r\n\r\n    this.createForm.updateValueAndValidity();\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAeC,SAAS,QAAQ,eAAe;AACzE,SACEC,SAAS,EACTC,WAAW,EAEXC,WAAW,EACXC,UAAU,QACL,gBAAgB;AACvB,SAAqBC,cAAc,QAAQ,qBAAqB;AAChE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,0BAA0B,QAAQ,yDAAyD;AAS7F,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EA4ClCC,YAAA;IA3CQ,KAAAC,EAAE,GAAgBX,MAAM,CAACG,WAAW,CAAC;IACrC,KAAAS,YAAY,GAAmBZ,MAAM,CAACM,cAAc,CAAC;IAE7D,KAAAO,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAA+B,CAAE,CAC3C;IACD,KAAAC,YAAY,GAAU,CACpB;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAI,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAK,CAAE,CAC/B;IAED,KAAAC,SAAS,GAAiC,CACxC;MAAEF,IAAI,EAAE,YAAY;MAAEG,EAAE,EAAE;IAAY,CAAE,EACxC;MAAEH,IAAI,EAAE,WAAW;MAAEG,EAAE,EAAE;IAAW,CAAE,EACtC;MAAEH,IAAI,EAAE,gBAAgB;MAAEG,EAAE,EAAE;IAAgB,CAAE,CACjD;IACD,KAAAC,gBAAgB,GAAmC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,CAAC;IAAC,CAAE;IAC7E,KAAAC,gBAAgB,GAAW,IAAI;IAM7B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC,OAAO,CAAC;MACtBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACvB,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAC3CC,cAAc,EAAE,CAAC,IAAI,CAACf,YAAY,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC;MAC3Cc,SAAS,EAAE,IAAI,CAACC,uBAAuB;KACxC,CAAC;EACJ;EAEAA,uBAAuBA,CAACC,IAAY;IAClC,MAAMC,SAAS,GAAG,IAAIhC,SAAS,CAAC,EAAE,CAAC;IACnC+B,IAAI,GAAGA,IAAI,IAAI,CAAC,IAAI,CAAClB,YAAY,CAAC,CAAC,CAAC,CAAC;IACrCkB,IAAI,EAAEE,OAAO,CAAEC,CAAC,IAAI;MAClBF,SAAS,CAACG,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAACF,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,OAAOF,SAAS;EAClB;EAEAI,sBAAsBA,CAACL,IAAU;IAC/B,OAAO,IAAI,CAACtB,EAAE,CAACe,KAAK,CAAC;MACnBa,YAAY,EAAEN,IAAI,EAAEhB,IAAI;MACxBuB,YAAY,EAAEP,IAAI,EAAEjB,IAAI;MACxByB,YAAY,EAAE,CAAC,IAAI,CAAC;MACpBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACrC,UAAU,CAACwB,QAAQ,CAAC;KAC3C,CAAC;EACJ;EAEA,IAAIc,MAAMA,CAAA;IACR,OAAO,IAAI,CAAClB,UAAU,CAACJ,KAAK;EAC9B;EAEAuB,oBAAoBA,CAACC,KAAqC,EAAEC,QAA0B;IACpF,IAAI,CAAC1B,gBAAgB,GAAGyB,KAAK;IAC7B,IAAI,CAACE,cAAc,GAAG,IAAI,CAACnC,YAAY,CAACoC,IAAI,CAACF,QAAQ,EAAE;MACrDG,QAAQ,EAAE;KACX,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACH,cAAc,CAACI,IAAI,EAAE;IAC1B,IAAI,IAAI,CAAC/B,gBAAgB,EAAEC,KAAK,EAAE;MAChC,IAAI,CAAC+B,iBAAiB,CAACC,gBAAgB,CAAC,IAAI,CAACjC,gBAAgB,CAAC;MAC9D,IAAI,CAACA,gBAAgB,GAAG;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,CAAC;MAAC,CAAE;IACpD;EACF;EAEAgC,mBAAmBA,CAACR,QAAgB,EAAExB,KAAK;IACxC,IAAI,CAACG,UAAU,CAAC8B,GAAG,CAAC,WAAW,CAAe,CAACC,EAAE,CAAClC,KAAK,CAAC,CAACmC,UAAU,CAAC;MAAEf,YAAY,EAAEI;IAAQ,CAAE,CAAC;EAClG;EAEAY,gBAAgBA,CAACZ,QAA0B;IACzC,IAAI,CAACa,eAAe,GAAG,IAAI,CAAC/C,YAAY,CAACoC,IAAI,CAACF,QAAQ,EAAE;MAAEG,QAAQ,EAAE;IAAI,CAAE,CAAC;EAC7E;EAEAW,WAAWA,CAAA;IACT,IAAI,CAACD,eAAe,EAAER,IAAI,EAAE;IAC5B,MAAMU,QAAQ,GAAG,IAAI,CAAC9C,YAAY,CAAC+C,IAAI,CAAC1B,CAAC,IAAIA,CAAC,EAAEnB,IAAI,KAAK,IAAI,CAACM,gBAAgB,CAAC;IAC/E,MAAMwC,aAAa,GAAG,IAAI,CAACzB,sBAAsB,CAACuB,QAAQ,CAAC;IAC1D,IAAI,CAACpC,UAAU,CAAC8B,GAAG,CAAC,WAAW,CAAe,CAAClB,IAAI,CAAC0B,aAAa,CAAC;IACnE,IAAI,CAACtC,UAAU,CAACuC,sBAAsB,EAAE;EAC1C;EAEAC,cAAcA,CAAC3C,KAAa;IAC1B,MAAM4C,cAAc,GAAG,IAAI,CAACzC,UAAU,CAAC8B,GAAG,CAAC,WAAW,CAAc;IAEpE;IACA,IAAIW,cAAc,CAACC,MAAM,IAAI,CAAC,EAAE;MAC9B;IACF;IAEA,MAAMC,mBAAmB,GAAGF,cAAc,CAACV,EAAE,CAAClC,KAAK,CAAC,CAACiC,GAAG,CAAC,cAAc,CAAC,EAAElC,KAAK;IAE/E;IACA6C,cAAc,CAACG,QAAQ,CAAC/C,KAAK,CAAC;IAE9B;IACA,IAAI,IAAI,CAACG,UAAU,CAAC8B,GAAG,CAAC,gBAAgB,CAAC,EAAElC,KAAK,KAAK+C,mBAAmB,EAAE;MACxE,MAAME,iBAAiB,GAAGJ,cAAc,CAACV,EAAE,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC,cAAc,CAAC,EAAElC,KAAK;MAC1E,IAAI,CAACI,UAAU,CAACgC,UAAU,CAAC;QAAE3B,cAAc,EAAEwC;MAAiB,CAAE,CAAC;IACnE;IAEA,IAAI,CAAC7C,UAAU,CAACuC,sBAAsB,EAAE;EAC1C;;;;;;;cAxFC/D,SAAS;QAAAsE,IAAA,GAAC,mBAAmB,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;MAAA;;;;AA1CtC/D,uBAAuB,GAAAgE,UAAA,EAPnC1E,SAAS,CAAC;EACT2E,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxE,WAAW,EAAEG,YAAY,EAAEC,0BAA0B,CAAC;EAChEsC,QAAA,EAAA+B,oBAA+C;;CAEhD,CAAC,C,EACWpE,uBAAuB,CAmInC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}