{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-trigger.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-trigger.component.scss?ngResource\";\nimport { Component, inject } from \"@angular/core\";\nimport { FormBuilder, FormsModule, Validators } from \"@angular/forms\";\nimport { SharedModule } from \"src/app/shared\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nlet CreateTriggerComponent = class CreateTriggerComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.modalService = inject(BsModalService);\n    this.selectedTriggerType = null;\n    this.tempXValue = 7;\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Create Communication Trigger\"\n    }];\n    this.triggerTypes = [{\n      id: 1,\n      name: \"On Xth day before due date\",\n      desc: \"Send communication X days before payment is due\",\n      xLabel: \"Days Before Due Date\",\n      detail: \"<PERSON><PERSON> will fire <<X>> days before the payment due date.\",\n      enabled: true\n    }, {\n      id: 2,\n      name: \"On Xth day after statement date\",\n      desc: \"Send communication X days after statement is generated\",\n      xLabel: \"Days After Statement Date\",\n      detail: \"Trigger will fire <<X>> days after the statement date.\",\n      enabled: true\n    }, {\n      id: 3,\n      name: \"On X DPD (Days Past Due)\",\n      desc: \"Send communication when account is X days past due\",\n      xLabel: \"Days Past Due\",\n      detail: \"Trigger will fire when an account reaches <<X>> days past due.\",\n      enabled: true\n    }, {\n      id: 4,\n      name: \"On PTP Date\",\n      desc: \"Send communication on Promise to Pay date\",\n      xLabel: \"\",\n      detail: \"Trigger will fire on the date when customer promised to pay.\",\n      enabled: true\n    }, {\n      id: 5,\n      name: \"On Broken PTP\",\n      desc: \"Re-try max for Z days when Promise to Pay is broken\",\n      xLabel: \"\",\n      detail: \"Trigger will fire when a customer breaks their promise to pay.\",\n      enabled: true\n    }, {\n      id: 6,\n      name: \"On Agency allocation change\",\n      desc: \"Re-try max for Z days when account is transferred to a new agency\",\n      xLabel: \"\",\n      detail: \"Trigger will fire when an account is allocated to a different collection agency.\",\n      enabled: true\n    }];\n    this.emailTemplates = [];\n    this.smsTemplates = [];\n    this.letterTemplates = [];\n    this.buildCreateTriggerForm();\n  }\n  buildCreateTriggerForm() {\n    this.createForm = this.fb.group({\n      triggerName: [null, [Validators.required]],\n      status: [true],\n      description: [null],\n      triggerType: [1],\n      xValue: [7],\n      activeChannel: [\"email\"],\n      emailTemplate: [null],\n      smsTemplate: [null],\n      letterTemplate: [null],\n      maxOccurrences: [\"1\"]\n    });\n  }\n  get fValue() {\n    return this.createForm.value;\n  }\n  get activeTriggerType() {\n    return this.triggerTypes.find(o => o?.id === this.fValue?.triggerType);\n  }\n  onTriggerTypeClick(triggerType, template) {\n    // If trigger type requires X value, show popup\n    if (triggerType.xLabel) {\n      this.selectedTriggerType = triggerType;\n      this.tempXValue = this.fValue?.xValue || 7;\n      const config = {\n        ignoreBackdropClick: true,\n        class: 'trigger-type-popup-modal'\n      };\n      this.modalRef = this.modalService.show(template, config);\n    } else {\n      // For trigger types that don't need X value, directly set the trigger type\n      this.createForm.patchValue({\n        triggerType: triggerType.id\n      });\n    }\n  }\n  onPopupOkay() {\n    if (this.selectedTriggerType && this.tempXValue) {\n      this.createForm.patchValue({\n        triggerType: this.selectedTriggerType.id,\n        xValue: this.tempXValue\n      });\n      this.modalRef?.hide();\n      this.selectedTriggerType = null;\n    }\n  }\n  onPopupCancel() {\n    this.modalRef?.hide();\n    this.selectedTriggerType = null;\n    this.tempXValue = 7;\n  }\n  getTriggerDetailText() {\n    if (this.selectedTriggerType && this.tempXValue) {\n      return this.selectedTriggerType.detail.replace('<<X>>', this.tempXValue.toString());\n    }\n    return '';\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nCreateTriggerComponent = __decorate([Component({\n  selector: \"app-create-trigger\",\n  standalone: true,\n  imports: [FormsModule, SharedModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTriggerComponent);\nexport { CreateTriggerComponent };", "map": {"version": 3, "names": ["Component", "inject", "FormBuilder", "FormsModule", "Validators", "SharedModule", "BsModalService", "CreateTriggerComponent", "constructor", "fb", "modalService", "selectedTriggerType", "tempXValue", "breadcrumbData", "label", "triggerTypes", "id", "name", "desc", "xLabel", "detail", "enabled", "emailTemplates", "smsTemplates", "letterTemplates", "buildCreateTriggerForm", "createForm", "group", "triggerName", "required", "status", "description", "triggerType", "xValue", "activeChannel", "emailTemplate", "smsTemplate", "letterTemplate", "maxOccurrences", "fValue", "value", "activeTriggerType", "find", "o", "onTriggerTypeClick", "template", "config", "ignoreBackdropClick", "class", "modalRef", "show", "patchValue", "onPopupOkay", "hide", "onPopupCancel", "getTriggerDetailText", "replace", "toString", "__decorate", "selector", "standalone", "imports", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\create-trigger\\create-trigger.component.ts"], "sourcesContent": ["import { Component, inject, TemplateRef } from \"@angular/core\";\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  FormsModule,\r\n  Validators,\r\n} from \"@angular/forms\";\r\nimport { SharedModule } from \"src/app/shared\";\r\nimport { BreadcrumbComponent } from \"src/app/shared/components/breadcrumb/breadcrumb.component\";\r\nimport { BsModalService, BsModalRef } from \"ngx-bootstrap/modal\";\r\n\r\n@Component({\r\n  selector: \"app-create-trigger\",\r\n  standalone: true,\r\n  imports: [FormsModule, SharedModule],\r\n  templateUrl: \"./create-trigger.component.html\",\r\n  styleUrl: \"./create-trigger.component.scss\",\r\n})\r\nexport class CreateTriggerComponent {\r\n  private fb: FormBuilder = inject(FormBuilder);\r\n  private modalService: BsModalService = inject(BsModalService);\r\n\r\n  modalRef?: BsModalRef;\r\n  selectedTriggerType: any = null;\r\n  tempXValue: number = 7;\r\n\r\n  breadcrumbData = [\r\n    { label: \"Communication\" },\r\n    { label: \"Create Communication Trigger\" },\r\n  ];\r\n\r\n  triggerTypes: any[] = [\r\n    {\r\n      id: 1,\r\n      name: \"On Xth day before due date\",\r\n      desc: \"Send communication X days before payment is due\",\r\n      xLabel: \"Days Before Due Date\",\r\n      detail: \"Trigger will fire <<X>> days before the payment due date.\",\r\n      enabled: true,\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"On Xth day after statement date\",\r\n      desc: \"Send communication X days after statement is generated\",\r\n      xLabel: \"Days After Statement Date\",\r\n      detail: \"Trigger will fire <<X>> days after the statement date.\",\r\n      enabled: true,\r\n    },\r\n    {\r\n      id: 3,\r\n      name: \"On X DPD (Days Past Due)\",\r\n      desc: \"Send communication when account is X days past due\",\r\n      xLabel: \"Days Past Due\",\r\n      detail: \"Trigger will fire when an account reaches <<X>> days past due.\",\r\n      enabled: true,\r\n    },\r\n    {\r\n      id: 4,\r\n      name: \"On PTP Date\",\r\n      desc: \"Send communication on Promise to Pay date\",\r\n      xLabel: \"\",\r\n      detail: \"Trigger will fire on the date when customer promised to pay.\",\r\n      enabled: true,\r\n    },\r\n    {\r\n      id: 5,\r\n      name: \"On Broken PTP\",\r\n      desc: \"Re-try max for Z days when Promise to Pay is broken\",\r\n      xLabel: \"\",\r\n      detail: \"Trigger will fire when a customer breaks their promise to pay.\",\r\n      enabled: true,\r\n    },\r\n    {\r\n      id: 6,\r\n      name: \"On Agency allocation change\",\r\n      desc: \"Re-try max for Z days when account is transferred to a new agency\",\r\n      xLabel: \"\",\r\n      detail:\r\n        \"Trigger will fire when an account is allocated to a different collection agency.\",\r\n      enabled: true,\r\n    },\r\n  ];\r\n  createForm!: FormGroup;\r\n  emailTemplates: any[] = [];\r\n  smsTemplates: any[] = [];\r\n  letterTemplates: any[] = [];\r\n\r\n  constructor() {\r\n    this.buildCreateTriggerForm();\r\n  }\r\n\r\n  buildCreateTriggerForm() {\r\n    this.createForm = this.fb.group({\r\n      triggerName: [null, [Validators.required]],\r\n      status: [true],\r\n      description: [null],\r\n      triggerType: [1],\r\n      xValue: [7],\r\n      activeChannel: [\"email\"],\r\n      emailTemplate: [null],\r\n      smsTemplate: [null],\r\n      letterTemplate: [null],\r\n      maxOccurrences: [\"1\"],\r\n    });\r\n  }\r\n\r\n  get fValue(): any {\r\n    return this.createForm.value;\r\n  }\r\n\r\n  get activeTriggerType(): any {\r\n    return this.triggerTypes.find((o) => o?.id === this.fValue?.triggerType);\r\n  }\r\n\r\n  onTriggerTypeClick(triggerType: any, template: TemplateRef<any>) {\r\n    // If trigger type requires X value, show popup\r\n    if (triggerType.xLabel) {\r\n      this.selectedTriggerType = triggerType;\r\n      this.tempXValue = this.fValue?.xValue || 7;\r\n\r\n      const config = {\r\n        ignoreBackdropClick: true,\r\n        class: 'trigger-type-popup-modal'\r\n      };\r\n      this.modalRef = this.modalService.show(template, config);\r\n    } else {\r\n      // For trigger types that don't need X value, directly set the trigger type\r\n      this.createForm.patchValue({ triggerType: triggerType.id });\r\n    }\r\n  }\r\n\r\n  onPopupOkay() {\r\n    if (this.selectedTriggerType && this.tempXValue) {\r\n      this.createForm.patchValue({\r\n        triggerType: this.selectedTriggerType.id,\r\n        xValue: this.tempXValue\r\n      });\r\n      this.modalRef?.hide();\r\n      this.selectedTriggerType = null;\r\n    }\r\n  }\r\n\r\n  onPopupCancel() {\r\n    this.modalRef?.hide();\r\n    this.selectedTriggerType = null;\r\n    this.tempXValue = 7;\r\n  }\r\n\r\n  getTriggerDetailText(): string {\r\n    if (this.selectedTriggerType && this.tempXValue) {\r\n      return this.selectedTriggerType.detail.replace('<<X>>', this.tempXValue.toString());\r\n    }\r\n    return '';\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAqB,eAAe;AAC9D,SACEC,WAAW,EAEXC,WAAW,EACXC,UAAU,QACL,gBAAgB;AACvB,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,cAAc,QAAoB,qBAAqB;AASzD,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAqEjCC,YAAA;IApEQ,KAAAC,EAAE,GAAgBR,MAAM,CAACC,WAAW,CAAC;IACrC,KAAAQ,YAAY,GAAmBT,MAAM,CAACK,cAAc,CAAC;IAG7D,KAAAK,mBAAmB,GAAQ,IAAI;IAC/B,KAAAC,UAAU,GAAW,CAAC;IAEtB,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAA8B,CAAE,CAC1C;IAED,KAAAC,YAAY,GAAU,CACpB;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,4BAA4B;MAClCC,IAAI,EAAE,iDAAiD;MACvDC,MAAM,EAAE,sBAAsB;MAC9BC,MAAM,EAAE,2DAA2D;MACnEC,OAAO,EAAE;KACV,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,wDAAwD;MAC9DC,MAAM,EAAE,2BAA2B;MACnCC,MAAM,EAAE,wDAAwD;MAChEC,OAAO,EAAE;KACV,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,0BAA0B;MAChCC,IAAI,EAAE,oDAAoD;MAC1DC,MAAM,EAAE,eAAe;MACvBC,MAAM,EAAE,gEAAgE;MACxEC,OAAO,EAAE;KACV,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,2CAA2C;MACjDC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,8DAA8D;MACtEC,OAAO,EAAE;KACV,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,qDAAqD;MAC3DC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,gEAAgE;MACxEC,OAAO,EAAE;KACV,EACD;MACEL,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,6BAA6B;MACnCC,IAAI,EAAE,mEAAmE;MACzEC,MAAM,EAAE,EAAE;MACVC,MAAM,EACJ,kFAAkF;MACpFC,OAAO,EAAE;KACV,CACF;IAED,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,eAAe,GAAU,EAAE;IAGzB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC,IAAI,EAAE,CAACxB,UAAU,CAACyB,QAAQ,CAAC,CAAC;MAC1CC,MAAM,EAAE,CAAC,IAAI,CAAC;MACdC,WAAW,EAAE,CAAC,IAAI,CAAC;MACnBC,WAAW,EAAE,CAAC,CAAC,CAAC;MAChBC,MAAM,EAAE,CAAC,CAAC,CAAC;MACXC,aAAa,EAAE,CAAC,OAAO,CAAC;MACxBC,aAAa,EAAE,CAAC,IAAI,CAAC;MACrBC,WAAW,EAAE,CAAC,IAAI,CAAC;MACnBC,cAAc,EAAE,CAAC,IAAI,CAAC;MACtBC,cAAc,EAAE,CAAC,GAAG;KACrB,CAAC;EACJ;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACb,UAAU,CAACc,KAAK;EAC9B;EAEA,IAAIC,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAAC1B,YAAY,CAAC2B,IAAI,CAAEC,CAAC,IAAKA,CAAC,EAAE3B,EAAE,KAAK,IAAI,CAACuB,MAAM,EAAEP,WAAW,CAAC;EAC1E;EAEAY,kBAAkBA,CAACZ,WAAgB,EAAEa,QAA0B;IAC7D;IACA,IAAIb,WAAW,CAACb,MAAM,EAAE;MACtB,IAAI,CAACR,mBAAmB,GAAGqB,WAAW;MACtC,IAAI,CAACpB,UAAU,GAAG,IAAI,CAAC2B,MAAM,EAAEN,MAAM,IAAI,CAAC;MAE1C,MAAMa,MAAM,GAAG;QACbC,mBAAmB,EAAE,IAAI;QACzBC,KAAK,EAAE;OACR;MACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACvC,YAAY,CAACwC,IAAI,CAACL,QAAQ,EAAEC,MAAM,CAAC;IAC1D,CAAC,MAAM;MACL;MACA,IAAI,CAACpB,UAAU,CAACyB,UAAU,CAAC;QAAEnB,WAAW,EAAEA,WAAW,CAAChB;MAAE,CAAE,CAAC;IAC7D;EACF;EAEAoC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzC,mBAAmB,IAAI,IAAI,CAACC,UAAU,EAAE;MAC/C,IAAI,CAACc,UAAU,CAACyB,UAAU,CAAC;QACzBnB,WAAW,EAAE,IAAI,CAACrB,mBAAmB,CAACK,EAAE;QACxCiB,MAAM,EAAE,IAAI,CAACrB;OACd,CAAC;MACF,IAAI,CAACqC,QAAQ,EAAEI,IAAI,EAAE;MACrB,IAAI,CAAC1C,mBAAmB,GAAG,IAAI;IACjC;EACF;EAEA2C,aAAaA,CAAA;IACX,IAAI,CAACL,QAAQ,EAAEI,IAAI,EAAE;IACrB,IAAI,CAAC1C,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,UAAU,GAAG,CAAC;EACrB;EAEA2C,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC5C,mBAAmB,IAAI,IAAI,CAACC,UAAU,EAAE;MAC/C,OAAO,IAAI,CAACD,mBAAmB,CAACS,MAAM,CAACoC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC5C,UAAU,CAAC6C,QAAQ,EAAE,CAAC;IACrF;IACA,OAAO,EAAE;EACX;;;;;AAvIWlD,sBAAsB,GAAAmD,UAAA,EAPlC1D,SAAS,CAAC;EACT2D,QAAQ,EAAE,oBAAoB;EAC9BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC1D,WAAW,EAAEE,YAAY,CAAC;EACpCwC,QAAA,EAAAiB,oBAA8C;;CAE/C,CAAC,C,EACWvD,sBAAsB,CAwIlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}