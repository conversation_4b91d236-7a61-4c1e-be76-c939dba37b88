{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-template.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-template.component.scss?ngResource\";\nimport { Component, inject, ViewChild } from \"@angular/core\";\nimport { FormArray, FormBuilder, FormsModule, Validators } from \"@angular/forms\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nimport { SharedModule } from \"src/app/shared\";\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\nlet CreateTemplateComponent = class CreateTemplateComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.modalService = inject(BsModalService);\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Create Communication Template\"\n    }];\n    this.allLanguages = [{\n      name: \"English\",\n      code: \"en\"\n    }, {\n      name: \"Hindi\",\n      code: \"hi\"\n    }, {\n      name: \"Tamil\",\n      code: \"ta\"\n    }, {\n      name: \"Bengali\",\n      code: \"bn\"\n    }, {\n      name: \"Telugu\",\n      code: \"te\"\n    }, {\n      name: \"Marathi\",\n      code: \"mr\"\n    }, {\n      name: \"Urdu\",\n      code: \"ur\"\n    }, {\n      name: \"Gujarati\",\n      code: \"gu\"\n    }, {\n      name: \"Kannada\",\n      code: \"kn\"\n    }, {\n      name: \"Malayalam\",\n      code: \"ml\"\n    }, {\n      name: \"Odia\",\n      code: \"or\"\n    }, {\n      name: \"Punjabi\",\n      code: \"pa\"\n    }, {\n      name: \"Assamese\",\n      code: \"as\"\n    }, {\n      name: \"Maithili\",\n      code: \"mai\"\n    }, {\n      name: \"Santali\",\n      code: \"sat\"\n    }, {\n      name: \"Kashmiri\",\n      code: \"ks\"\n    }, {\n      name: \"Konkani\",\n      code: \"kok\"\n    }, {\n      name: \"Sindhi\",\n      code: \"sd\"\n    }, {\n      name: \"Sanskrit\",\n      code: \"sa\"\n    }, {\n      name: \"Manipuri\",\n      code: \"mni\"\n    }, {\n      name: \"Bodo\",\n      code: \"brx\"\n    }, {\n      name: \"Dogri\",\n      code: \"doi\"\n    }];\n    this.variables = [{\n      name: \"First Name\",\n      id: \"FIRST_NAME\"\n    }, {\n      name: \"Last Name\",\n      id: \"LAST_NAME\"\n    }, {\n      name: \"Overdue Amount\",\n      id: \"OVERDUE_AMOUNT\"\n    }];\n    this.selectedVariable = {\n      value: null,\n      index: -1\n    };\n    this.selectedLanguage = null;\n    this.buildCreateTemplateForm();\n  }\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\"],\n      templateName: [null, [Validators.required]],\n      activeLanguage: [this.allLanguages[0].code],\n      languages: this.buildLanguagesFormArray()\n    });\n  }\n  buildLanguagesFormArray(data) {\n    const formArray = new FormArray([]);\n    data = data || [this.allLanguages[0]];\n    data?.forEach(o => {\n      formArray.push(this.buildLanguageFormGroup(o));\n    });\n    return formArray;\n  }\n  buildLanguageFormGroup(data) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null, [Validators.required]]\n    });\n  }\n  get fValue() {\n    return this.createForm.value;\n  }\n  get availableLanguages() {\n    const selectedLanguageCodes = this.fValue?.languages?.map(lang => lang.languageCode) || [];\n    return this.allLanguages.filter(lang => !selectedLanguageCodes.includes(lang.code));\n  }\n  openMapVariableModal(event, template) {\n    this.selectedVariable = event;\n    this.mapVarModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  assignVariable() {\n    this.mapVarModalRef.hide();\n    if (this.selectedVariable?.value) {\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\n      this.selectedVariable = {\n        value: null,\n        index: -1\n      };\n    }\n  }\n  updateTemplateValue(template, index) {\n    this.createForm.get('languages').at(index).patchValue({\n      templateBody: template\n    });\n  }\n  openAddLangModal(template) {\n    // Reset selected language to ensure it doesn't show a filtered out language\n    this.selectedLanguage = null;\n    this.addLangModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  addLanguage() {\n    this.addLangModalRef?.hide();\n    // Check if language is already selected\n    const selectedLanguageCodes = this.fValue?.languages?.map(lang => lang.languageCode) || [];\n    if (selectedLanguageCodes.includes(this.selectedLanguage)) {\n      return; // Language already exists, don't add\n    }\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\n    const langFormGroup = this.buildLanguageFormGroup(language);\n    this.createForm.get('languages').push(langFormGroup);\n    this.createForm.updateValueAndValidity();\n  }\n  removeLanguage(index) {\n    const languagesArray = this.createForm.get('languages');\n    // Prevent removing the last language\n    if (languagesArray.length <= 1) {\n      return;\n    }\n    const removedLanguage = languagesArray.at(index).value;\n    languagesArray.removeAt(index);\n    // If the removed language was the active one, switch to the first available language\n    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      if (firstLanguage) {\n        this.createForm.patchValue({\n          activeLanguage: firstLanguage.languageCode\n        });\n      }\n    }\n    this.createForm.updateValueAndValidity();\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      templateVarConfig: [{\n        type: ViewChild,\n        args: ['templateVarConfig', {\n          static: false\n        }]\n      }]\n    };\n  }\n};\nCreateTemplateComponent = __decorate([Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [FormsModule, SharedModule, TemplateVarConfigDirective],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTemplateComponent);\nexport { CreateTemplateComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "FormArray", "FormBuilder", "FormsModule", "Validators", "BsModalService", "SharedModule", "TemplateVarConfigDirective", "CreateTemplateComponent", "constructor", "fb", "modalService", "breadcrumbData", "label", "allLanguages", "name", "code", "variables", "id", "selectedVariable", "value", "index", "selectedLanguage", "buildCreateTemplateForm", "createForm", "group", "channelType", "templateName", "required", "activeLanguage", "languages", "buildLanguagesFormArray", "data", "formArray", "for<PERSON>ach", "o", "push", "buildLanguageFormGroup", "languageCode", "languageName", "emailSubject", "templateBody", "fValue", "availableLanguages", "selectedLanguageCodes", "map", "lang", "filter", "includes", "openMapVariableModal", "event", "template", "mapVarModalRef", "show", "animated", "assignVariable", "hide", "templateVarConfig", "onUpdateVariable", "updateTemplateValue", "get", "at", "patchValue", "openAddLangModal", "addLangModalRef", "addLanguage", "language", "find", "langFormGroup", "updateValueAndValidity", "removeLanguage", "languagesArray", "length", "removedLanguage", "removeAt", "firstLanguage", "args", "static", "__decorate", "selector", "standalone", "imports", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\permissionscreen\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\create-template.component.ts"], "sourcesContent": ["import { Component, inject, TemplateRef, ViewChild } from \"@angular/core\";\r\nimport {\r\n  FormArray,\r\n  FormBuilder,\r\n  FormGroup,\r\n  FormsModule,\r\n  Validators,\r\n} from \"@angular/forms\";\r\nimport { BsModalRef, BsModalService } from \"ngx-bootstrap/modal\";\r\nimport { SharedModule } from \"src/app/shared\";\r\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\r\n\r\n@Component({\r\n  selector: \"app-create-template\",\r\n  standalone: true,\r\n  imports: [FormsModule, SharedModule, TemplateVarConfigDirective],\r\n  templateUrl: \"./create-template.component.html\",\r\n  styleUrl: \"./create-template.component.scss\",\r\n})\r\nexport class CreateTemplateComponent {\r\n  private fb: FormBuilder = inject(FormBuilder);\r\n  private modalService: BsModalService = inject(BsModalService);\r\n\r\n  breadcrumbData = [\r\n    { label: \"Communication\" },\r\n    { label: \"Create Communication Template\" },\r\n  ];\r\n  allLanguages: any[] = [\r\n    { name: \"English\", code: \"en\" },\r\n    { name: \"Hindi\", code: \"hi\" },\r\n    { name: \"Tamil\", code: \"ta\" },\r\n    { name: \"Bengali\", code: \"bn\" },\r\n    { name: \"Telugu\", code: \"te\" },\r\n    { name: \"Marathi\", code: \"mr\" },\r\n    { name: \"Urdu\", code: \"ur\" },\r\n    { name: \"Gujarati\", code: \"gu\" },\r\n    { name: \"Kannada\", code: \"kn\" },\r\n    { name: \"Malayalam\", code: \"ml\" },\r\n    { name: \"Odia\", code: \"or\" },\r\n    { name: \"Punjabi\", code: \"pa\" },\r\n    { name: \"Assamese\", code: \"as\" },\r\n    { name: \"Maithili\", code: \"mai\" },\r\n    { name: \"Santali\", code: \"sat\" },\r\n    { name: \"Kashmiri\", code: \"ks\" },\r\n    { name: \"Konkani\", code: \"kok\" },\r\n    { name: \"Sindhi\", code: \"sd\" },\r\n    { name: \"Sanskrit\", code: \"sa\" },\r\n    { name: \"Manipuri\", code: \"mni\" },\r\n    { name: \"Bodo\", code: \"brx\" },\r\n    { name: \"Dogri\", code: \"doi\" },\r\n  ];\r\n  createForm!: FormGroup;\r\n  variables: {name: string, id: string}[] = [\r\n    { name: \"First Name\", id: \"FIRST_NAME\" },\r\n    { name: \"Last Name\", id: \"LAST_NAME\" },\r\n    { name: \"Overdue Amount\", id: \"OVERDUE_AMOUNT\" },\r\n  ];\r\n  selectedVariable: {value: string, index: number} = { value: null, index: -1 };\r\n  selectedLanguage: string = null;\r\n  mapVarModalRef!: BsModalRef;\r\n  addLangModalRef!: BsModalRef;\r\n  @ViewChild('templateVarConfig', { static: false }) templateVarConfig!: TemplateVarConfigDirective;\r\n\r\n  constructor() {\r\n    this.buildCreateTemplateForm();\r\n  }\r\n\r\n  buildCreateTemplateForm() {\r\n    this.createForm = this.fb.group({\r\n      channelType: [\"email\"],\r\n      templateName: [null, [Validators.required]],\r\n      activeLanguage: [this.allLanguages[0].code],\r\n      languages: this.buildLanguagesFormArray(),\r\n    });\r\n  }\r\n\r\n  buildLanguagesFormArray(data?: any[]) {\r\n    const formArray = new FormArray([]);\r\n    data = data || [this.allLanguages[0]];\r\n    data?.forEach((o) => {\r\n      formArray.push(this.buildLanguageFormGroup(o));\r\n    });\r\n    return formArray;\r\n  }\r\n\r\n  buildLanguageFormGroup(data?: any) {\r\n    return this.fb.group({\r\n      languageCode: data?.code,\r\n      languageName: data?.name,\r\n      emailSubject: [null],\r\n      templateBody: [null, [Validators.required]],\r\n    });\r\n  }\r\n\r\n  get fValue(): any {\r\n    return this.createForm.value;\r\n  }\r\n\r\n  get availableLanguages(): any[] {\r\n    const selectedLanguageCodes = this.fValue?.languages?.map(lang => lang.languageCode) || [];\r\n    return this.allLanguages.filter(lang => !selectedLanguageCodes.includes(lang.code));\r\n  }\r\n\r\n  openMapVariableModal(event: {index: number, value: string}, template: TemplateRef<any>) {\r\n    this.selectedVariable = event;\r\n    this.mapVarModalRef = this.modalService.show(template, {\r\n      animated: true,\r\n    })\r\n  }\r\n\r\n  assignVariable() {\r\n    this.mapVarModalRef.hide();\r\n    if (this.selectedVariable?.value) {\r\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\r\n      this.selectedVariable = { value: null, index: -1 };\r\n    }\r\n  }\r\n\r\n  updateTemplateValue(template: string, index) {\r\n    (this.createForm.get('languages') as FormArray).at(index).patchValue({ templateBody: template });\r\n  }\r\n\r\n  openAddLangModal(template: TemplateRef<any>) {\r\n    // Reset selected language to ensure it doesn't show a filtered out language\r\n    this.selectedLanguage = null;\r\n    this.addLangModalRef = this.modalService.show(template, { animated: true });\r\n  }\r\n\r\n  addLanguage() {\r\n    this.addLangModalRef?.hide();\r\n\r\n    // Check if language is already selected\r\n    const selectedLanguageCodes = this.fValue?.languages?.map(lang => lang.languageCode) || [];\r\n    if (selectedLanguageCodes.includes(this.selectedLanguage)) {\r\n      return; // Language already exists, don't add\r\n    }\r\n\r\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\r\n    const langFormGroup = this.buildLanguageFormGroup(language);\r\n    (this.createForm.get('languages') as FormArray).push(langFormGroup);\r\n    this.createForm.updateValueAndValidity();\r\n  }\r\n\r\n  removeLanguage(index: number) {\r\n    const languagesArray = this.createForm.get('languages') as FormArray;\r\n\r\n    // Prevent removing the last language\r\n    if (languagesArray.length <= 1) {\r\n      return;\r\n    }\r\n\r\n    const removedLanguage = languagesArray.at(index).value;\r\n    languagesArray.removeAt(index);\r\n\r\n    // If the removed language was the active one, switch to the first available language\r\n    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {\r\n      const firstLanguage = languagesArray.at(0)?.value;\r\n      if (firstLanguage) {\r\n        this.createForm.patchValue({ activeLanguage: firstLanguage.languageCode });\r\n      }\r\n    }\r\n\r\n    this.createForm.updateValueAndValidity();\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAeC,SAAS,QAAQ,eAAe;AACzE,SACEC,SAAS,EACTC,WAAW,EAEXC,WAAW,EACXC,UAAU,QACL,gBAAgB;AACvB,SAAqBC,cAAc,QAAQ,qBAAqB;AAChE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,0BAA0B,QAAQ,yDAAyD;AAS7F,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EA4ClCC,YAAA;IA3CQ,KAAAC,EAAE,GAAgBX,MAAM,CAACG,WAAW,CAAC;IACrC,KAAAS,YAAY,GAAmBZ,MAAM,CAACM,cAAc,CAAC;IAE7D,KAAAO,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAA+B,CAAE,CAC3C;IACD,KAAAC,YAAY,GAAU,CACpB;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAI,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAK,CAAE,CAC/B;IAED,KAAAC,SAAS,GAAiC,CACxC;MAAEF,IAAI,EAAE,YAAY;MAAEG,EAAE,EAAE;IAAY,CAAE,EACxC;MAAEH,IAAI,EAAE,WAAW;MAAEG,EAAE,EAAE;IAAW,CAAE,EACtC;MAAEH,IAAI,EAAE,gBAAgB;MAAEG,EAAE,EAAE;IAAgB,CAAE,CACjD;IACD,KAAAC,gBAAgB,GAAmC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,CAAC;IAAC,CAAE;IAC7E,KAAAC,gBAAgB,GAAW,IAAI;IAM7B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MAC9BC,WAAW,EAAE,CAAC,OAAO,CAAC;MACtBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACvB,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAC3CC,cAAc,EAAE,CAAC,IAAI,CAACf,YAAY,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC;MAC3Cc,SAAS,EAAE,IAAI,CAACC,uBAAuB;KACxC,CAAC;EACJ;EAEAA,uBAAuBA,CAACC,IAAY;IAClC,MAAMC,SAAS,GAAG,IAAIhC,SAAS,CAAC,EAAE,CAAC;IACnC+B,IAAI,GAAGA,IAAI,IAAI,CAAC,IAAI,CAAClB,YAAY,CAAC,CAAC,CAAC,CAAC;IACrCkB,IAAI,EAAEE,OAAO,CAAEC,CAAC,IAAI;MAClBF,SAAS,CAACG,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAACF,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IACF,OAAOF,SAAS;EAClB;EAEAI,sBAAsBA,CAACL,IAAU;IAC/B,OAAO,IAAI,CAACtB,EAAE,CAACe,KAAK,CAAC;MACnBa,YAAY,EAAEN,IAAI,EAAEhB,IAAI;MACxBuB,YAAY,EAAEP,IAAI,EAAEjB,IAAI;MACxByB,YAAY,EAAE,CAAC,IAAI,CAAC;MACpBC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACrC,UAAU,CAACwB,QAAQ,CAAC;KAC3C,CAAC;EACJ;EAEA,IAAIc,MAAMA,CAAA;IACR,OAAO,IAAI,CAAClB,UAAU,CAACJ,KAAK;EAC9B;EAEA,IAAIuB,kBAAkBA,CAAA;IACpB,MAAMC,qBAAqB,GAAG,IAAI,CAACF,MAAM,EAAEZ,SAAS,EAAEe,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACR,YAAY,CAAC,IAAI,EAAE;IAC1F,OAAO,IAAI,CAACxB,YAAY,CAACiC,MAAM,CAACD,IAAI,IAAI,CAACF,qBAAqB,CAACI,QAAQ,CAACF,IAAI,CAAC9B,IAAI,CAAC,CAAC;EACrF;EAEAiC,oBAAoBA,CAACC,KAAqC,EAAEC,QAA0B;IACpF,IAAI,CAAChC,gBAAgB,GAAG+B,KAAK;IAC7B,IAAI,CAACE,cAAc,GAAG,IAAI,CAACzC,YAAY,CAAC0C,IAAI,CAACF,QAAQ,EAAE;MACrDG,QAAQ,EAAE;KACX,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACH,cAAc,CAACI,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACrC,gBAAgB,EAAEC,KAAK,EAAE;MAChC,IAAI,CAACqC,iBAAiB,CAACC,gBAAgB,CAAC,IAAI,CAACvC,gBAAgB,CAAC;MAC9D,IAAI,CAACA,gBAAgB,GAAG;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,CAAC;MAAC,CAAE;IACpD;EACF;EAEAsC,mBAAmBA,CAACR,QAAgB,EAAE9B,KAAK;IACxC,IAAI,CAACG,UAAU,CAACoC,GAAG,CAAC,WAAW,CAAe,CAACC,EAAE,CAACxC,KAAK,CAAC,CAACyC,UAAU,CAAC;MAAErB,YAAY,EAAEU;IAAQ,CAAE,CAAC;EAClG;EAEAY,gBAAgBA,CAACZ,QAA0B;IACzC;IACA,IAAI,CAAC7B,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC0C,eAAe,GAAG,IAAI,CAACrD,YAAY,CAAC0C,IAAI,CAACF,QAAQ,EAAE;MAAEG,QAAQ,EAAE;IAAI,CAAE,CAAC;EAC7E;EAEAW,WAAWA,CAAA;IACT,IAAI,CAACD,eAAe,EAAER,IAAI,EAAE;IAE5B;IACA,MAAMZ,qBAAqB,GAAG,IAAI,CAACF,MAAM,EAAEZ,SAAS,EAAEe,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACR,YAAY,CAAC,IAAI,EAAE;IAC1F,IAAIM,qBAAqB,CAACI,QAAQ,CAAC,IAAI,CAAC1B,gBAAgB,CAAC,EAAE;MACzD,OAAO,CAAC;IACV;IAEA,MAAM4C,QAAQ,GAAG,IAAI,CAACpD,YAAY,CAACqD,IAAI,CAAChC,CAAC,IAAIA,CAAC,EAAEnB,IAAI,KAAK,IAAI,CAACM,gBAAgB,CAAC;IAC/E,MAAM8C,aAAa,GAAG,IAAI,CAAC/B,sBAAsB,CAAC6B,QAAQ,CAAC;IAC1D,IAAI,CAAC1C,UAAU,CAACoC,GAAG,CAAC,WAAW,CAAe,CAACxB,IAAI,CAACgC,aAAa,CAAC;IACnE,IAAI,CAAC5C,UAAU,CAAC6C,sBAAsB,EAAE;EAC1C;EAEAC,cAAcA,CAACjD,KAAa;IAC1B,MAAMkD,cAAc,GAAG,IAAI,CAAC/C,UAAU,CAACoC,GAAG,CAAC,WAAW,CAAc;IAEpE;IACA,IAAIW,cAAc,CAACC,MAAM,IAAI,CAAC,EAAE;MAC9B;IACF;IAEA,MAAMC,eAAe,GAAGF,cAAc,CAACV,EAAE,CAACxC,KAAK,CAAC,CAACD,KAAK;IACtDmD,cAAc,CAACG,QAAQ,CAACrD,KAAK,CAAC;IAE9B;IACA,IAAI,IAAI,CAACqB,MAAM,EAAEb,cAAc,KAAK4C,eAAe,EAAEnC,YAAY,EAAE;MACjE,MAAMqC,aAAa,GAAGJ,cAAc,CAACV,EAAE,CAAC,CAAC,CAAC,EAAEzC,KAAK;MACjD,IAAIuD,aAAa,EAAE;QACjB,IAAI,CAACnD,UAAU,CAACsC,UAAU,CAAC;UAAEjC,cAAc,EAAE8C,aAAa,CAACrC;QAAY,CAAE,CAAC;MAC5E;IACF;IAEA,IAAI,CAACd,UAAU,CAAC6C,sBAAsB,EAAE;EAC1C;;;;;;;cAtGCrE,SAAS;QAAA4E,IAAA,GAAC,mBAAmB,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;MAAA;;;;AA1CtCrE,uBAAuB,GAAAsE,UAAA,EAPnChF,SAAS,CAAC;EACTiF,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC9E,WAAW,EAAEG,YAAY,EAAEC,0BAA0B,CAAC;EAChE4C,QAAA,EAAA+B,oBAA+C;;CAEhD,CAAC,C,EACW1E,uBAAuB,CAiJnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}