import { Component } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { SharedModule } from "src/app/shared";
import { BreadcrumbComponent } from "src/app/shared/components/breadcrumb/breadcrumb.component";

@Component({
  selector: "app-search-templates",
  standalone: true,
  imports: [FormsModule, SharedModule],
  templateUrl: "./search-templates.component.html",
  styleUrl: "./search-templates.component.scss",
})
export class SearchTemplatesComponent {
  breadcrumbData = [
    { label: "Communication" },
    { label: "Search Communication Templates" },
  ];
  hasAccess = {
    create: true,
    view: true,
    edit: true,
  };

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 5;
  templates = [
    {
      id: "1",
      templateName: "Payment Reminder Template",
      description: "Email Template for Payment Reminder",
      channel: "Email",
      languages: ["en"],
      createdBy: "<PERSON>",
      createdOn: "2024-01-15",
      countOfTriggers: 3,
      status: "Active"
    },
    {
      id: "2",
      templateName: "Welcome Email Template",
      description: "Email Template for New User Welcome",
      channel: "Email",
      languages: ["en", "es"],
      createdBy: "Jane Smith",
      createdOn: "2024-01-10",
      countOfTriggers: 5,
      status: "Inactive"
    },
    {
      id: "3",
      templateName: "SMS Notification Template",
      description: "SMS Template for Payment Notifications",
      channel: "SMS",
      languages: ["en"],
      createdBy: "Mike Johnson",
      createdOn: "2024-01-20",
      countOfTriggers: 2,
      status: "Active"
    },
    {
      id: "4",
      templateName: "Account Suspension Notice",
      description: "Email Template for Account Suspension",
      channel: "Email",
      languages: ["en", "fr"],
      createdBy: "Sarah Wilson",
      createdOn: "2024-01-05",
      countOfTriggers: 1,
      status: "Inactive"
    },
    {
      id: "5",
      templateName: "Monthly Statement Template",
      description: "Email Template for Monthly Statements",
      channel: "Email",
      languages: ["en"],
      createdBy: "David Brown",
      createdOn: "2024-01-25",
      countOfTriggers: 8,
      status: "Active"
    },
  ];

  // Filter
  filter = {
    searchTerm: "",
    channel: null,
  };

  isLoading = false;
}
