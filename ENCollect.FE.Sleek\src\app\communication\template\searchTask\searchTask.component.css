.dfb {
  margin: 20px 0;
  font-family: '<PERSON><PERSON>', sans-serif;
}

.dfb__holder {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.dfb__header {
  background: rgba(48, 48, 71, 1.0);
  padding: 0 10px;
}

.dfb__header__title {
  color: rgba(255, 255, 255, 1.0);
  font-size: 20px;
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 1px;
  margin: 10px;
}

.dfb__form {
  background: rgba(249, 249, 249, 1.0);
  padding: 0 15px;
  padding-bottom: 5px;
}

.dfb__form__holder {
  padding: 10px;
  float: left;
  width: 100%;
}

.dfb__form__holder__label {

}

.dfb__form__holder__label label {
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dfb__form__holder__label label sup {
  color: rgba(204, 51, 0, 1.0);
}

.dfb__form__holder__label.sublabel label {
  color: rgba(66, 66, 66, 0.5);
  font-size: 12px;
}

.dfb__form__holder__element {

}

.dfb__form__holder__mandatory-note {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dfb__form__holder__mandatory-note sup {
  color: rgba(204, 51, 0, 1.0);
}

.dfb__form__holder__element input,
.dfb__form__holder__element input:focus,
.dfb__form__holder__element input:hover,
.dfb__form__holder__element select,
.dfb__form__holder__element select:focus,
.dfb__form__holder__element select:hover  {
  border-radius: 0px;
  border-color: transparent;
  outline: 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.dfb__form__holder__element select[multiple] {
  height: 150px;
}

.dfb__form__holder__element button {
  border-radius: 0px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dfb__form__holder__element button:hover, .dfb__form__holder__element button:focus, .dfb__form__holder__element button:active, .dfb__form__holder__element button:visited {
  outline: 0;
}

.dfb__form__holder__element button.dfb__transfer-button {
  display: block;
  margin: 0 auto;
  background: #333;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  color: rgba(255, 255, 255, 1.0);
  margin-top: 10px;
}

.dfb__transfer-button-holder {

}

.dfb__accordion {

}

.dfb__accordion.inner__accordion .panel-body {
  background: rgba(250, 253, 255, 1.0);
}

.dfb__accordion .panel-heading {
  background: rgba(48, 48, 71, 1.0);
  border-radius: 0px;
}

.dfb__accordion .panel-title {
  font-weight: 700;
  text-transform: uppercase;
  font-size: 15px;
  letter-spacing: 1px;
}

.dfb__accordion .panel-title a {
  color: rgba(255, 255, 255, 1.0);
}

.dfb__accordion .panel-title a:hover, .dfb__accordion .panel-title a:focus, .dfb__accordion .panel-title a:active, .dfb__accordion .panel-title a:visited {
  text-decoration: none;
}

.dfb__accordion .panel-group .panel {
  border-radius: 0px;
}

.dfb__nav-tabs {
  background: #FFF none repeat scroll 0% 0%;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3);
  margin-bottom: 30px;
}

.dfb__nav-tabs .nav-tabs {
  border-bottom: 2px solid #DDD;
}

.dfb__nav-tabs .nav-tabs > li.active > a,
.dfb__nav-tabs .nav-tabs > li.active > a:focus,
.dfb__nav-tabs .nav-tabs > li.active > a:hover {
  border-width: 0;
}

.dfb__nav-tabs .nav-tabs > li > a {
  border: none;
  color: #666;
}

.dfb__nav-tabs .nav-tabs > li.active > a,
.dfb__nav-tabs .nav-tabs > li > a:hover {
  border: none;
  color: #4285F4 !important;
  background: transparent;
}

.dfb__nav-tabs .nav-tabs > li > a::after {
  content: "";
  background: #4285F4;
  height: 2px;
  position: absolute;
  width: 100%;
  left: 0px;
  bottom: -1px;
  transition: all 250ms ease 0s;
  transform: scale(0);
}

.dfb__nav-tabs .nav-tabs > li.active > a::after,
.dfb__nav-tabs .nav-tabs > li:hover > a::after {
  transform: scale(1);
}

.dfb__nav-tabs  .tab-nav > li > a::after {
  background: #21527d none repeat scroll 0% 0%; color: #fff;
}

.dfb__nav-tabs .tab-pane {
  padding: 15px 0;
}

.dfb__nav-tabs .tab-content {
  padding:20px;
}

.dfb__nav-tabs__vertical {
  background: #FFF none repeat scroll 0% 0%;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3);
}

.dfb__nav-tabs__vertical .nav-tabs {
  border-bottom: none;
  border-right: 0;
}

.dfb__nav-tabs__vertical .nav-tabs > li.active > a,
.dfb__nav-tabs__vertical .nav-tabs > li.active > a:focus,
.dfb__nav-tabs__vertical .nav-tabs > li.active > a:hover {
  border-width: 0;
}

.dfb__nav-tabs__vertical .nav-tabs > li > a {
  border: none;
  color: #666;
}

.dfb__nav-tabs__vertical .nav-tabs > li.active > a,
.dfb__nav-tabs__vertical .nav-tabs > li > a:hover {
  border: none;
  color: #4285F4 !important;
  background: transparent;
}

.dfb__nav-tabs__vertical .nav-tabs > li > a::after {
  content: "";
  background: #4285F4;
  width: 2px;
  position: absolute;
  height: 100%;
  right: -1px;
  top: 0px;
  transition: all 250ms ease 0s;
  transform: scale(0);
  transform-origin:top right;
}

.dfb__nav-tabs__vertical .nav-tabs > li.active > a::after,
.dfb__nav-tabs__vertical .nav-tabs > li:hover > a::after {
  transform: scale(1);
}

.dfb__nav-tabs__vertical  .tab-nav > li > a::after {
  background: #21527d none repeat scroll 0% 0%;
  color: #fff;
}

.dfb__nav-tabs__vertical .tab-pane {
  padding: 15px 0;
}

.dfb__nav-tabs__vertical .tab-content {
  padding:20px;
}
.fieldInsideSection{
	border-radius: 65px;
    padding: 3px;
    background-color: #ccc;
    font-size: 9px;
    /* width: 50px; */
    color: black;
    position: relative;
    top: -13px;
    left: -29px;
    text-align: center
}
.tabsCloseIcon{
	border-radius: 65px;
    padding: 3px;
    background-color: #ccc;
    font-size: 9px;
    /* width: 50px; */
    color: black;
    position: relative;
    top: -13px;
    left: 2px;
    text-align: center
}
.sectionCloseIcon{
	border-radius: 65px;
    padding: 3px;
    background-color: #ccc;
    font-size: 9px;
    /* width: 50px; */
    color: black;
    position: absolute;
    top: -1px;
    left: -3px;
    text-align: center
}

/* Modal close button styling to match side drawer */
::ng-deep .modal-close-btn {
    width: 1.625rem;
    height: 1.625rem;
    cursor: pointer;
    border-radius: 50%;
}